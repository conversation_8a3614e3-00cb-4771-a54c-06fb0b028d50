import{k as f,W as k,Q as c,X as r,a1 as B,c as m,m as o,Y as L,O as u,R as a,K as e,S as t,l as D,v as O,a2 as V,j as q,$ as g,F as K,P as T,z as j,a3 as P,Z as R}from"./vendor-RHijBMdK.js";import{a as A,u as E,_ as F}from"./index-hmWk46K2.js";import{D as H}from"./SpeedometerOutline-D-Vl8HvQ.js";import{K as U}from"./Key-DZgkvFqF.js";import{M as G}from"./DesktopOutline-Dcvn7Ag4.js";import{S as Q}from"./Settings-CsD-lPn5.js";import{P as y}from"./Person-Cj8AC7xs.js";import{L as W,M as X,a as Y}from"./Moon-V5Og8Ghg.js";import{h as s,m as b,n as Z,o as J,p as ee,B as _,q as te,r as ae,s as oe,t as le}from"./ui-DUh7fRR5.js";import{M as se}from"./Mail-DFX8DOvB.js";const ne={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},re=r("rect",{x:"48",y:"96",width:"416",height:"320",rx:"40",ry:"40",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),ie=r("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M112 160l144 112l144-112"},null,-1),ce=[re,ie],de=f({name:"MailOutline",render:function(p,d){return c(),k("svg",ne,ce)}}),ue={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},pe=B('<path d="M256 118a22 22 0 0 1-22-22V48a22 22 0 0 1 44 0v48a22 22 0 0 1-22 22z" fill="currentColor"></path><path d="M256 486a22 22 0 0 1-22-22v-48a22 22 0 0 1 44 0v48a22 22 0 0 1-22 22z" fill="currentColor"></path><path d="M369.14 164.86a22 22 0 0 1-15.56-37.55l33.94-33.94a22 22 0 0 1 31.11 31.11l-33.94 33.94a21.93 21.93 0 0 1-15.55 6.44z" fill="currentColor"></path><path d="M108.92 425.08a22 22 0 0 1-15.55-37.56l33.94-33.94a22 22 0 1 1 31.11 31.11l-33.94 33.94a21.94 21.94 0 0 1-15.56 6.45z" fill="currentColor"></path><path d="M464 278h-48a22 22 0 0 1 0-44h48a22 22 0 0 1 0 44z" fill="currentColor"></path><path d="M96 278H48a22 22 0 0 1 0-44h48a22 22 0 0 1 0 44z" fill="currentColor"></path><path d="M403.08 425.08a21.94 21.94 0 0 1-15.56-6.45l-33.94-33.94a22 22 0 0 1 31.11-31.11l33.94 33.94a22 22 0 0 1-15.55 37.56z" fill="currentColor"></path><path d="M142.86 164.86a21.89 21.89 0 0 1-15.55-6.44l-33.94-33.94a22 22 0 0 1 31.11-31.11l33.94 33.94a22 22 0 0 1-15.56 37.55z" fill="currentColor"></path><path d="M256 358a102 102 0 1 1 102-102a102.12 102.12 0 0 1-102 102z" fill="currentColor"></path>',9),he=[pe],me=f({name:"Sunny",render:function(p,d){return c(),k("svg",ue,he)}}),_e={class:"sidebar-header"},fe={class:"logo"},ke={class:"logo-text"},ve={class:"header-left"},we={class:"header-right"},ge={class:"username"},ye={class:"content-wrapper"},be=f({__name:"MainLayout",setup(v){const p=R(),d=L(),n=A(),w=E(),C=m(()=>[{label:"系统概览",key:"/dashboard",icon:()=>o(s,null,{default:()=>o(H)})},{label:"激活管理",key:"/activation-codes",icon:()=>o(s,null,{default:()=>o(U)})},{label:"邮箱管理",key:"/mailbox-management",icon:()=>o(s,null,{default:()=>o(de)})},{label:"系统监控",key:"/system-monitor",icon:()=>o(s,null,{default:()=>o(G)})},{label:"系统配置",key:"/system-config",icon:()=>o(s,null,{default:()=>o(Q)})}]),M=m(()=>d.path),x=m(()=>{const i=[{title:"首页",path:"/"}];return d.meta.title&&i.push({title:d.meta.title,path:d.path}),i}),S=m(()=>[{label:"个人设置",key:"profile",icon:()=>o(s,null,{default:()=>o(y)})},{type:"divider",key:"divider"},{label:"退出登录",key:"logout",icon:()=>o(s,null,{default:()=>o(W)})}]),I=i=>{p.push(i)},N=i=>{switch(i){case"profile":p.push("/profile");break;case"logout":w.logout();break}},z=()=>{n.setTheme(n.isDark?"light":"dark")};return(i,h)=>{const $=T("router-view");return c(),u(e(b),{"has-sider":"",class:"main-layout"},{default:a(()=>[t(e(J),{bordered:"","collapse-mode":"width","collapsed-width":64,width:240,collapsed:e(n).sidebarCollapsed,"show-trigger":"",onCollapse:h[0]||(h[0]=l=>e(n).setSidebarCollapsed(!0)),onExpand:h[1]||(h[1]=l=>e(n).setSidebarCollapsed(!1))},{default:a(()=>[r("div",_e,[r("div",fe,[t(e(s),{size:"32",color:"#18a058"},{default:a(()=>[t(e(se))]),_:1}),D(r("span",ke,"Go-Mail",512),[[O,!e(n).sidebarCollapsed]])])]),t(e(Z),{collapsed:e(n).sidebarCollapsed,"collapsed-width":64,"collapsed-icon-size":22,options:C.value,value:M.value,"onUpdate:value":I},null,8,["collapsed","options","value"])]),_:1},8,["collapsed"]),t(e(b),null,{default:a(()=>[t(e(ee),{bordered:"",class:"header"},{default:a(()=>[r("div",ve,[t(e(_),{quaternary:"",onClick:e(n).toggleSidebar,class:"sidebar-toggle"},{icon:a(()=>[t(e(s),null,{default:a(()=>[t(e(X))]),_:1})]),_:1},8,["onClick"]),t(e(te),{class:"breadcrumb"},{default:a(()=>[(c(!0),k(K,null,V(x.value,l=>(c(),u(e(ae),{key:l.path,clickable:!!l.path,onClick:Ce=>l.path&&i.$router.push(l.path)},{default:a(()=>[q(g(l.title),1)]),_:2},1032,["clickable","onClick"]))),128))]),_:1})]),r("div",we,[t(e(_),{quaternary:"",onClick:z,class:"theme-toggle"},{icon:a(()=>[t(e(s),null,{default:a(()=>[e(n).isDark?(c(),u(e(me),{key:0})):(c(),u(e(Y),{key:1}))]),_:1})]),_:1}),t(e(oe),{options:S.value,onSelect:N},{default:a(()=>[t(e(_),{quaternary:"",class:"user-menu"},{icon:a(()=>[t(e(s),null,{default:a(()=>[t(e(y))]),_:1})]),default:a(()=>{var l;return[r("span",ge,g((l=e(w).user)==null?void 0:l.username),1)]}),_:1})]),_:1},8,["options"])])]),_:1}),t(e(le),{class:"content"},{default:a(()=>[r("div",ye,[t($,null,{default:a(({Component:l})=>[t(j,{name:"fade",mode:"out-in"},{default:a(()=>[(c(),u(P(l)))]),_:2},1024)]),_:1})])]),_:1})]),_:1})]),_:1})}}}),Oe=F(be,[["__scopeId","data-v-4f3a898c"]]);export{Oe as default};

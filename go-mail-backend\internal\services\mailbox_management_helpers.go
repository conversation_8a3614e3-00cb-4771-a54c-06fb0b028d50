package services

import (
	"context"
	"crypto/rand"
	"database/sql"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"go-mail/internal/database"
	"go-mail/internal/types"
	"math"
	"regexp"
	"sync"
	"time"
)

// 对象池优化 - 减少内存分配
var (
	// VerificationContext对象池
	verificationContextPool = sync.Pool{
		New: func() interface{} {
			return &VerificationContext{}
		},
	}

	// 字符串缓冲区池
	stringBufferPool = sync.Pool{
		New: func() interface{} {
			return make([]byte, 0, 1024)
		},
	}
)

// DBRetryConfig 数据库重试配置
type DBRetryConfig struct {
	MaxRetries    int           // 最大重试次数
	BaseDelay     time.Duration // 基础延迟
	MaxDelay      time.Duration // 最大延迟
	BackoffFactor float64       // 退避因子
}

// DefaultDBRetryConfig 默认数据库重试配置 - 针对高并发优化
func DefaultDBRetryConfig() DBRetryConfig {
	return DBRetryConfig{
		MaxRetries:    8,                     // 增加重试次数
		BaseDelay:     50 * time.Millisecond, // 减少基础延迟
		MaxDelay:      3 * time.Second,       // 减少最大延迟
		BackoffFactor: 1.5,                   // 减少退避因子，更快重试
	}
}

// ConcurrencyMonitor 并发监控器
type ConcurrencyMonitor struct {
	activeSlots map[int]string // slot -> email
	semaphore   chan struct{}
	mutex       sync.RWMutex
	logger      interface {
		Info(msg string, args ...interface{})
	}
}

// NewConcurrencyMonitor 创建并发监控器
func NewConcurrencyMonitor(limit int, logger interface {
	Info(msg string, args ...interface{})
}) *ConcurrencyMonitor {
	return &ConcurrencyMonitor{
		activeSlots: make(map[int]string),
		semaphore:   make(chan struct{}, limit),
		logger:      logger,
	}
}

// AcquireSlot 获取并发槽位
func (m *ConcurrencyMonitor) AcquireSlot(email string) int {
	m.semaphore <- struct{}{}
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 找到可用槽位
	slot := m.findAvailableSlot()
	m.activeSlots[slot] = email

	m.logger.Info("获取并发槽位",
		"email", email,
		"slot", slot,
		"active_slots", len(m.activeSlots))

	return slot
}

// ReleaseSlot 释放并发槽位
func (m *ConcurrencyMonitor) ReleaseSlot(slot int, email string) {
	m.mutex.Lock()
	delete(m.activeSlots, slot)
	activeCount := len(m.activeSlots)
	m.mutex.Unlock()

	<-m.semaphore

	m.logger.Info("释放并发槽位",
		"email", email,
		"slot", slot,
		"remaining_active_slots", activeCount)
}

// findAvailableSlot 查找可用槽位
func (m *ConcurrencyMonitor) findAvailableSlot() int {
	for i := 0; ; i++ {
		if _, exists := m.activeSlots[i]; !exists {
			return i
		}
	}
}

// GetActiveSlots 获取活跃槽位信息
func (m *ConcurrencyMonitor) GetActiveSlots() map[int]string {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	result := make(map[int]string)
	for slot, email := range m.activeSlots {
		result[slot] = email
	}
	return result
}

// ProgressTracker 进度跟踪器
type ProgressTracker struct {
	Total      int
	Completed  int
	Success    int
	Failed     int
	StartTime  time.Time
	LastUpdate time.Time
	mutex      sync.RWMutex
}

// NewProgressTracker 创建进度跟踪器
func NewProgressTracker(total int) *ProgressTracker {
	return &ProgressTracker{
		Total:     total,
		StartTime: time.Now(),
	}
}

// Update 更新进度
func (p *ProgressTracker) Update(success bool) ProgressStats {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	p.Completed++
	if success {
		p.Success++
	} else {
		p.Failed++
	}
	p.LastUpdate = time.Now()

	return p.GetStats()
}

// GetStats 获取统计信息
func (p *ProgressTracker) GetStats() ProgressStats {
	elapsed := time.Since(p.StartTime)
	remaining := p.Total - p.Completed

	var eta time.Duration
	if p.Completed > 0 {
		avgTimePerTask := elapsed / time.Duration(p.Completed)
		eta = avgTimePerTask * time.Duration(remaining)
	}

	successRate := float64(0)
	if p.Completed > 0 {
		successRate = float64(p.Success) / float64(p.Completed) * 100
	}

	return ProgressStats{
		Total:       p.Total,
		Completed:   p.Completed,
		Success:     p.Success,
		Failed:      p.Failed,
		Remaining:   remaining,
		SuccessRate: successRate,
		Elapsed:     elapsed,
		ETA:         eta,
	}
}

// ProgressStats 进度统计信息
type ProgressStats struct {
	Total       int
	Completed   int
	Success     int
	Failed      int
	Remaining   int
	SuccessRate float64
	Elapsed     time.Duration
	ETA         time.Duration
}

// calculateBackoffDelay 计算退避延迟
func calculateBackoffDelay(attempt int, config DBRetryConfig) time.Duration {
	delay := time.Duration(float64(config.BaseDelay) * math.Pow(config.BackoffFactor, float64(attempt)))
	if delay > config.MaxDelay {
		delay = config.MaxDelay
	}
	return delay
}

// executeWithRetry 执行数据库操作并重试
func (s *MailboxManagementService) executeWithRetry(ctx context.Context, operation string, fn func() error) error {
	config := DefaultDBRetryConfig()

	for attempt := 0; attempt <= config.MaxRetries; attempt++ {
		err := fn()
		if err == nil {
			if attempt > 0 {
				s.logger.Info("数据库操作重试成功",
					"operation", operation,
					"attempt", attempt+1,
					"total_attempts", config.MaxRetries+1)
			}
			return nil
		}

		// 检查是否是数据库锁定错误
		if !isDatabaseBusyError(err) {
			s.logger.Error("数据库操作失败（非锁定错误）",
				"operation", operation,
				"attempt", attempt+1,
				"error", err)
			return err
		}

		// 如果是最后一次尝试，返回错误
		if attempt == config.MaxRetries {
			s.logger.Error("数据库操作重试次数耗尽",
				"operation", operation,
				"total_attempts", attempt+1,
				"final_error", err)
			return fmt.Errorf("数据库操作失败，重试%d次后仍然失败: %w", config.MaxRetries, err)
		}

		// 计算退避延迟
		delay := calculateBackoffDelay(attempt, config)
		s.logger.Warn("数据库锁定，准备重试",
			"operation", operation,
			"attempt", attempt+1,
			"delay", delay,
			"error", err)

		// 等待后重试
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(delay):
			// 继续重试
		}
	}

	return fmt.Errorf("数据库操作重试逻辑异常")
}

// isDatabaseBusyError 检查是否是数据库忙碌错误
func isDatabaseBusyError(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()
	return contains(errStr, "database is locked") ||
		contains(errStr, "SQLITE_BUSY") ||
		contains(errStr, "database table is locked") ||
		contains(errStr, "cannot start a transaction within a transaction")
}

// contains 检查字符串是否包含子字符串（不区分大小写）
func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			(len(s) > len(substr) &&
				(s[:len(substr)] == substr ||
					s[len(s)-len(substr):] == substr ||
					indexOfSubstring(s, substr) >= 0)))
}

// indexOfSubstring 查找子字符串位置
func indexOfSubstring(s, substr string) int {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}

// VerificationContext 验证上下文
type VerificationContext struct {
	OperationID    string
	Email          string
	TaskID         string
	StartTime      time.Time
	ConcurrentSlot int
	RetryAttempt   int
	Logger         interface {
		Info(msg string, args ...interface{})
		Error(msg string, args ...interface{})
		Debug(msg string, args ...interface{})
	}
}

// LogInfo 记录信息日志
func (ctx *VerificationContext) LogInfo(msg string, fields ...interface{}) {
	allFields := []interface{}{
		"operation_id", ctx.OperationID,
		"email", ctx.Email,
		"task_id", ctx.TaskID,
		"concurrent_slot", ctx.ConcurrentSlot,
		"elapsed", time.Since(ctx.StartTime),
	}
	if ctx.RetryAttempt > 0 {
		allFields = append(allFields, "retry_attempt", ctx.RetryAttempt)
	}
	allFields = append(allFields, fields...)
	ctx.Logger.Info(msg, allFields...)
}

// LogError 记录错误日志
func (ctx *VerificationContext) LogError(msg string, fields ...interface{}) {
	allFields := []interface{}{
		"operation_id", ctx.OperationID,
		"email", ctx.Email,
		"task_id", ctx.TaskID,
		"concurrent_slot", ctx.ConcurrentSlot,
		"elapsed", time.Since(ctx.StartTime),
	}
	if ctx.RetryAttempt > 0 {
		allFields = append(allFields, "retry_attempt", ctx.RetryAttempt)
	}
	allFields = append(allFields, fields...)
	ctx.Logger.Error(msg, allFields...)
}

// LogDebug 记录调试日志
func (ctx *VerificationContext) LogDebug(msg string, fields ...interface{}) {
	allFields := []interface{}{
		"operation_id", ctx.OperationID,
		"email", ctx.Email,
		"task_id", ctx.TaskID,
		"concurrent_slot", ctx.ConcurrentSlot,
		"elapsed", time.Since(ctx.StartTime),
	}
	if ctx.RetryAttempt > 0 {
		allFields = append(allFields, "retry_attempt", ctx.RetryAttempt)
	}
	allFields = append(allFields, fields...)
	ctx.Logger.Debug(msg, allFields...)
}

// generateOperationID 生成操作ID
func (s *MailboxManagementService) generateOperationID() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// generateBatchImportID 生成批量导入ID
func (s *MailboxManagementService) generateBatchImportID() string {
	return fmt.Sprintf("batch_%d", time.Now().Unix())
}

// isValidEmail 验证邮箱格式
func (s *MailboxManagementService) isValidEmail(email string) bool {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

// accountExists 检查账户是否已存在
func (s *MailboxManagementService) accountExists(email string) bool {
	var count int
	err := s.db.GetDB().QueryRow("SELECT COUNT(*) FROM accounts WHERE email = ?", email).Scan(&count)
	return err == nil && count > 0
}

// saveBatchOperation 保存批量操作记录
func (s *MailboxManagementService) saveBatchOperation(batchOp *database.BatchOperation) error {
	query := `INSERT INTO batch_operations (
		operation_id, operation_type, status, total_count, processed_count, 
		success_count, failed_count, error_message, created_by, created_at, 
		started_at, completed_at, progress_data, operation_params
	) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	_, err := s.db.GetDB().Exec(query,
		batchOp.OperationID, batchOp.OperationType, batchOp.Status, batchOp.TotalCount,
		batchOp.ProcessedCount, batchOp.SuccessCount, batchOp.FailedCount, batchOp.ErrorMessage,
		batchOp.CreatedBy, batchOp.CreatedAt, batchOp.StartedAt, batchOp.CompletedAt,
		batchOp.ProgressData, batchOp.OperationParams,
	)

	return err
}

// saveExtendedAccount 保存扩展账户信息
func (s *MailboxManagementService) saveExtendedAccount(account *database.ExtendedAccount) error {
	query := `INSERT INTO accounts (
		email, password, cookie_data, login_status, last_login_time, email_status,
		usage_status, usage_id, session_id, jsession_id, navigator_sid, created_at, updated_at,
		batch_import_id, verification_status, last_verification_time, verification_error,
		is_disabled, import_source, tags
	) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	_, err := s.db.GetDB().Exec(query,
		account.Email, account.Password, account.CookieData, account.LoginStatus,
		account.LastLoginTime, account.EmailStatus, account.UsageStatus, account.UsageID,
		account.SessionID, account.JSessionID, account.NavigatorSID, account.CreatedAt,
		account.UpdatedAt, account.BatchImportID, account.VerificationStatus,
		account.LastVerificationTime, account.VerificationError, account.IsDisabled,
		account.ImportSource, account.Tags,
	)

	return err
}

// updateBatchOperationStatus 更新批量操作状态（增强版本，带重试机制）
func (s *MailboxManagementService) updateBatchOperationStatus(operationID, status string, startedAt, completedAt *time.Time) error {
	return s.executeWithRetry(context.Background(), "update_batch_operation_status", func() error {
		query := "UPDATE batch_operations SET status = ?, started_at = ?, completed_at = ? WHERE operation_id = ?"
		_, err := s.db.GetDB().Exec(query, status, startedAt, completedAt, operationID)
		if err != nil {
			s.logger.Debug("更新批量操作状态失败",
				"operation_id", operationID,
				"status", status,
				"error", err)
		}
		return err
	})
}

// updateBatchOperationProgress 更新批量操作进度（增强版本，带重试机制）
func (s *MailboxManagementService) updateBatchOperationProgress(operationID string, processed, success, failed int) error {
	return s.executeWithRetry(context.Background(), "update_batch_operation_progress", func() error {
		query := "UPDATE batch_operations SET processed_count = ?, success_count = ?, failed_count = ? WHERE operation_id = ?"
		_, err := s.db.GetDB().Exec(query, processed, success, failed, operationID)
		if err != nil {
			s.logger.Debug("更新批量操作进度失败",
				"operation_id", operationID,
				"processed", processed,
				"success", success,
				"failed", failed,
				"error", err)
		}
		return err
	})
}

// updateBatchOperationCounts 更新批量操作计数（增强版本，带重试机制）
func (s *MailboxManagementService) updateBatchOperationCounts(operationID string, total, success, failed int) error {
	return s.executeWithRetry(context.Background(), "update_batch_operation_counts", func() error {
		query := "UPDATE batch_operations SET total_count = ?, success_count = ?, failed_count = ? WHERE operation_id = ?"
		_, err := s.db.GetDB().Exec(query, total, success, failed, operationID)
		if err != nil {
			s.logger.Debug("更新批量操作计数失败",
				"operation_id", operationID,
				"total", total,
				"success", success,
				"failed", failed,
				"error", err)
		}
		return err
	})
}

// createVerificationTask 创建验证任务
func (s *MailboxManagementService) createVerificationTask(operationID, batchImportID string) error {
	// 记录方法开始执行
	s.logger.Info("开始创建验证任务",
		"operation_id", operationID,
		"batch_import_id", batchImportID,
		"method", "createVerificationTask")

	// 获取需要验证的账户
	s.logger.Debug("查询批量导入的邮箱账户",
		"operation_id", operationID,
		"batch_import_id", batchImportID,
		"query", "SELECT email FROM accounts WHERE batch_import_id = ?")

	query := "SELECT email FROM accounts WHERE batch_import_id = ?"
	rows, err := s.db.GetDB().Query(query, batchImportID)
	if err != nil {
		s.logger.Error("查询邮箱账户失败",
			"operation_id", operationID,
			"batch_import_id", batchImportID,
			"error", err,
			"query", query)
		return err
	}
	defer rows.Close()

	var emails []string
	for rows.Next() {
		var email string
		if err := rows.Scan(&email); err != nil {
			s.logger.Warn("扫描邮箱记录失败",
				"operation_id", operationID,
				"batch_import_id", batchImportID,
				"error", err)
			continue
		}
		emails = append(emails, email)
	}

	s.logger.Info("成功获取待验证邮箱列表",
		"operation_id", operationID,
		"batch_import_id", batchImportID,
		"email_count", len(emails),
		"emails", emails)

	// 为每个邮箱创建任务日志记录
	s.logger.Info("开始为邮箱账户创建任务日志记录",
		"operation_id", operationID,
		"batch_import_id", batchImportID,
		"total_emails", len(emails))

	now := time.Now()
	taskIDMap := make(map[string]string) // email -> taskID 映射
	successCount := 0
	failedCount := 0

	for i, email := range emails {
		// 生成任务ID（使用固定时间戳确保一致性）
		taskID := fmt.Sprintf("verify_%s_%s_%d", operationID, email, now.Unix())
		taskIDMap[email] = taskID

		s.logger.Debug("为邮箱创建任务日志记录",
			"operation_id", operationID,
			"batch_import_id", batchImportID,
			"email", email,
			"task_id", taskID,
			"progress", fmt.Sprintf("%d/%d", i+1, len(emails)))

		// 创建任务日志记录
		createReq := database.CreateTaskLogRequest{
			TaskID:        taskID,
			OperationType: "verify",
			Email:         email,
			Status:        "pending",
			StartTime:     now,
			BatchID:       &batchImportID,
		}

		// 创建任务日志记录
		if taskLog, err := s.taskLogService.CreateTaskLog(createReq); err != nil {
			failedCount++
			s.logger.Error("创建验证任务日志失败",
				"operation_id", operationID,
				"batch_import_id", batchImportID,
				"email", email,
				"task_id", taskID,
				"error", err,
				"progress", fmt.Sprintf("%d/%d", i+1, len(emails)))
			// 继续处理其他邮箱，不因单个失败而中断
			continue
		} else {
			successCount++
			s.logger.Debug("成功创建验证任务日志",
				"operation_id", operationID,
				"batch_import_id", batchImportID,
				"email", email,
				"task_id", taskID,
				"log_id", taskLog.ID,
				"detail_log_path", taskLog.DetailLogPath,
				"progress", fmt.Sprintf("%d/%d", i+1, len(emails)))
		}
	}

	s.logger.Info("任务日志创建完成",
		"operation_id", operationID,
		"batch_import_id", batchImportID,
		"total_emails", len(emails),
		"success_count", successCount,
		"failed_count", failedCount,
		"success_rate", fmt.Sprintf("%.2f%%", float64(successCount)/float64(len(emails))*100))

	// 创建验证任务请求
	s.logger.Info("准备启动验证任务",
		"operation_id", operationID,
		"batch_import_id", batchImportID,
		"verification_type", "login",
		"concurrent_limit", 10,
		"retry_limit", 3,
		"task_log_success_count", successCount)

	req := &database.VerificationTaskRequest{
		AccountEmails:    emails,
		VerificationType: "login",
		ConcurrentLimit:  10,
		RetryLimit:       3,
	}

	// 检查是否有成功创建的任务日志
	if successCount == 0 {
		s.logger.Error("没有成功创建任何任务日志记录，取消验证任务启动",
			"operation_id", operationID,
			"batch_import_id", batchImportID,
			"total_emails", len(emails),
			"failed_count", failedCount)
		return fmt.Errorf("没有成功创建任何任务日志记录")
	}

	// 异步执行验证
	s.logger.Info("启动异步验证任务",
		"operation_id", operationID,
		"batch_import_id", batchImportID,
		"emails_to_verify", len(emails),
		"task_logs_created", successCount,
		"goroutine_started", true)

	go s.executeVerificationTaskWithLogging(context.Background(), operationID, batchImportID, taskIDMap, req)

	// 记录方法执行完成
	s.logger.Info("验证任务创建完成",
		"operation_id", operationID,
		"batch_import_id", batchImportID,
		"method", "createVerificationTask",
		"total_emails", len(emails),
		"task_logs_created", successCount,
		"task_logs_failed", failedCount,
		"verification_task_started", true)

	return nil
}

// executeVerificationTaskWithLogging
func (s *MailboxManagementService) executeVerificationTaskWithLogging(ctx context.Context, operationID, batchImportID string, taskIDMap map[string]string, req *database.VerificationTaskRequest) {
	startTime := time.Now()

	// 检查传入的上下文类型和状态
	ctxType := "unknown"
	if ctx == context.Background() {
		ctxType = "background"
	} else if ctx == context.TODO() {
		ctxType = "todo"
	} else {
		ctxType = "custom"
	}

	// 详细的任务开始日志（带源码位置）
	s.logger.Info("开始执行验证任务",
		"operation_id", operationID,
		"batch_import_id", batchImportID,
		"account_count", len(req.AccountEmails),
		"concurrent_limit", req.ConcurrentLimit,
		"verification_type", req.VerificationType,
		"retry_limit", req.RetryLimit,
		"start_time", startTime,
		"input_context_type", ctxType,
		"taskIDMap_provided", taskIDMap != nil,
		"taskIDMap_size", func() int {
			if taskIDMap != nil {
				return len(taskIDMap)
			}
			return 0
		}(),
		"input_context_done_check", func() bool {
			select {
			case <-ctx.Done():
				return true
			default:
				return false
			}
		}(),
		"input_context_error", func() string {
			if ctx.Err() != nil {
				return ctx.Err().Error()
			}
			return "none"
		}())

	// 添加 taskIDMap 验证日志
	if taskIDMap == nil {
		s.logger.Info("ERROR", "taskIDMap 为 nil，这将导致任务ID查找失败",
			"operation_id", operationID,
			"account_count", len(req.AccountEmails))
	} else {
		s.logger.Info("DEBUG", "taskIDMap 验证通过",
			"operation_id", operationID,
			"taskIDMap_size", len(taskIDMap),
			"expected_size", len(req.AccountEmails))
	}

	// 使用数据库重试机制更新操作状态为运行中
	err := s.executeWithRetry(ctx, "update_batch_status_running", func() error {
		return s.updateBatchOperationStatus(operationID, "running", &[]time.Time{time.Now()}[0], nil)
	})
	if err != nil {
		s.logger.Error("更新批量操作状态为运行中失败",
			"operation_id", operationID,
			"error", err)
		return
	}

	// 创建并发监控器和进度跟踪器
	concurrencyMonitor := NewConcurrencyMonitor(req.ConcurrentLimit, s.logger)
	progressTracker := NewProgressTracker(len(req.AccountEmails))

	// 进度更新控制变量
	lastProgressUpdate := time.Now()

	// 创建结果通道
	results := make(chan struct {
		email    string
		success  bool
		slot     int
		duration time.Duration
	}, len(req.AccountEmails))

	// 创建带超时的上下文
	timeoutDuration := 10 * time.Minute
	verifyCtx, cancel := context.WithTimeout(ctx, timeoutDuration)
	defer cancel()

	// 添加详细的超时调试信息（带源码位置）
	s.logger.Info("INFO", "验证任务初始化完成",
		"operation_id", operationID,
		"concurrent_limit", req.ConcurrentLimit,
		"timeout_duration", timeoutDuration,
		"timeout_deadline", time.Now().Add(timeoutDuration),
		"total_accounts", len(req.AccountEmails),
		"context_deadline_set", true)

	// 检查传入的上下文是否已经有超时
	if deadline, ok := ctx.Deadline(); ok {
		s.logger.Info("WARN", "传入的上下文已有超时设置",
			"operation_id", operationID,
			"parent_deadline", deadline,
			"current_time", time.Now(),
			"parent_timeout_remaining", time.Until(deadline),
			"new_timeout_duration", timeoutDuration)

		// 如果父上下文的超时时间比我们设置的还短，这可能是立即超时的原因
		if time.Until(deadline) < timeoutDuration {
			s.logger.Info("ERROR", "父上下文超时时间过短，可能导致立即超时",
				"operation_id", operationID,
				"parent_timeout_remaining", time.Until(deadline),
				"required_timeout", timeoutDuration)
		}
	} else {
		s.logger.Info("INFO", "父上下文无超时限制，使用新设置的超时",
			"operation_id", operationID,
			"timeout_duration", timeoutDuration)
	}

	// 并发执行验证（使用增强的并发控制和日志记录）
	currentTime := time.Now()
	deadline, hasDeadline := verifyCtx.Deadline()
	s.logger.Info("INFO", "开始启动验证goroutines",
		"operation_id", operationID,
		"total_goroutines", len(req.AccountEmails),
		"current_time", currentTime,
		"has_deadline", hasDeadline,
		"deadline", deadline,
		"time_until_deadline", func() interface{} {
			if hasDeadline {
				return time.Until(deadline)
			}
			return "no_deadline"
		}(),
		"context_done_check", func() bool {
			select {
			case <-verifyCtx.Done():
				return true
			default:
				return false
			}
		}())

	// 如果上下文已经超时，立即报告
	select {
	case <-verifyCtx.Done():
		s.logger.Info("ERROR", "上下文在启动goroutines前已经超时",
			"operation_id", operationID,
			"context_error", verifyCtx.Err(),
			"deadline", deadline,
			"current_time", currentTime)
		return
	default:
		s.logger.Info("INFO", "上下文状态正常，开始启动goroutines",
			"operation_id", operationID)
	}

	for i, email := range req.AccountEmails {
		s.logger.Info("DEBUG", "准备启动验证goroutine",
			"operation_id", operationID,
			"email", email,
			"goroutine_index", i+1,
			"total_goroutines", len(req.AccountEmails))

		go func(email string, index int) {
			emailStartTime := time.Now()

			// 立即检查上下文状态
			select {
			case <-verifyCtx.Done():
				s.logger.Info("ERROR", "goroutine启动时发现上下文已取消",
					"operation_id", operationID,
					"email", email,
					"goroutine_index", index,
					"context_error", verifyCtx.Err(),
					"elapsed_since_start", time.Since(emailStartTime))
				return
			default:
				s.logger.Info("DEBUG", "goroutine正常启动",
					"operation_id", operationID,
					"email", email,
					"goroutine_index", index)
			}

			// 获取并发槽位（替代信号量）
			slot := concurrencyMonitor.AcquireSlot(email)
			defer concurrencyMonitor.ReleaseSlot(slot, email)

			// 获取任务ID（与创建时保持一致）
			taskID, exists := taskIDMap[email]
			if !exists {
				s.logger.Error("找不到邮箱对应的任务ID",
					"operation_id", operationID,
					"email", email,
					"concurrent_slot", slot)
				return
			}

			// 从对象池获取验证上下文，减少内存分配
			verifyContext := verificationContextPool.Get().(*VerificationContext)
			// 重置对象状态
			*verifyContext = VerificationContext{
				OperationID:    operationID,
				Email:          email,
				TaskID:         taskID,
				StartTime:      emailStartTime,
				ConcurrentSlot: slot,
				Logger:         s.logger,
			}

			// 确保在函数结束时回收对象
			defer func() {
				// 清理敏感信息
				verifyContext.Email = ""
				verifyContext.TaskID = ""
				verifyContext.Logger = nil
				verificationContextPool.Put(verifyContext)
			}()

			verifyContext.LogInfo("开始邮箱验证",
				"verification_type", req.VerificationType,
				"timeout", "30秒")

			// 使用数据库重试机制更新任务日志状态为运行中
			err := s.executeWithRetry(verifyCtx, "update_task_log_running", func() error {
				updateReq := database.UpdateTaskLogRequest{
					Status: &[]string{"running"}[0],
				}
				return s.taskLogService.UpdateTaskLog(taskID, updateReq)
			})
			if err != nil {
				verifyContext.LogError("更新任务日志状态为运行中失败", "error", err)
				return
			}

			verifyContext.LogDebug("任务日志状态已更新为运行中")

			// 使用带超时的上下文进行验证
			verifyContext.LogInfo("开始执行账户验证")
			success := s.verifyAccountWithTimeoutEnhanced(verifyCtx, verifyContext, req.VerificationType)

			// 计算验证耗时
			verificationDuration := time.Since(emailStartTime)

			verifyContext.LogInfo("账户验证完成",
				"success", success,
				"duration", verificationDuration)

			// 使用数据库重试机制更新任务日志状态
			err = s.executeWithRetry(verifyCtx, "update_task_log_final", func() error {
				endTime := time.Now()
				if success {
					updateReq := database.UpdateTaskLogRequest{
						Status:  &[]string{"success"}[0],
						EndTime: &endTime,
					}
					return s.taskLogService.UpdateTaskLog(taskID, updateReq)
				} else {
					errorMsg := "验证失败"
					updateReq := database.UpdateTaskLogRequest{
						Status:       &[]string{"failed"}[0],
						EndTime:      &endTime,
						ErrorMessage: &errorMsg,
					}
					return s.taskLogService.UpdateTaskLog(taskID, updateReq)
				}
			})
			if err != nil {
				verifyContext.LogError("更新任务日志最终状态失败", "error", err)
			} else {
				verifyContext.LogDebug("任务日志最终状态更新成功")
			}

			// 非阻塞发送结果
			select {
			case results <- struct {
				email    string
				success  bool
				slot     int
				duration time.Duration
			}{email, success, slot, verificationDuration}:
				verifyContext.LogDebug("验证结果已发送到结果通道")
			case <-verifyCtx.Done():
				verifyContext.LogError("验证任务超时，无法发送结果")
				return
			}
		}(email, i)
	}

	// 收集结果（增强版本，带详细进度跟踪）
	completed := 0
	collectStartTime := time.Now()
	s.logger.Info("INFO", "开始收集验证结果",
		"operation_id", operationID,
		"expected_results", len(req.AccountEmails),
		"collect_start_time", collectStartTime,
		"context_deadline", func() time.Time {
			if d, ok := verifyCtx.Deadline(); ok {
				return d
			} else {
				return time.Time{}
			}
		}(),
		"time_until_deadline", func() time.Duration {
			if d, ok := verifyCtx.Deadline(); ok {
				return time.Until(d)
			} else {
				return 0
			}
		}(),
		"context_done_immediate_check", func() bool {
			select {
			case <-verifyCtx.Done():
				return true
			default:
				return false
			}
		}())

	for completed < len(req.AccountEmails) {
		loopStartTime := time.Now()

		// 在每次循环开始时检查上下文状态
		select {
		case <-verifyCtx.Done():
			s.logger.Info("ERROR", "结果收集循环开始时发现上下文已取消",
				"operation_id", operationID,
				"completed", completed,
				"total", len(req.AccountEmails),
				"context_error", verifyCtx.Err(),
				"loop_iteration_time", loopStartTime,
				"elapsed_since_collect_start", time.Since(collectStartTime),
				"elapsed_since_task_start", time.Since(startTime))
			// 立即执行超时处理并退出循环
			finalStats := progressTracker.GetStats()
			s.logger.Info("WARN", "验证任务超时",
				"operation_id", operationID,
				"completed", finalStats.Completed,
				"total", len(req.AccountEmails),
				"success", finalStats.Success,
				"failed", finalStats.Failed,
				"timeout_duration", time.Since(startTime))
			completed = len(req.AccountEmails) // 强制退出循环
			break
		default:
			// 上下文正常，继续处理
			if completed%10 == 0 || completed < 5 { // 每10个结果或前5个结果记录一次
				s.logger.Info("DEBUG", "结果收集循环正常进行",
					"operation_id", operationID,
					"completed", completed,
					"total", len(req.AccountEmails),
					"loop_iteration", completed+1)
			}
		}

		select {
		case result := <-results:
			completed++

			// 更新进度跟踪器
			stats := progressTracker.Update(result.success)

			s.logger.Info("收到验证结果",
				"operation_id", operationID,
				"email", result.email,
				"success", result.success,
				"slot", result.slot,
				"duration", result.duration,
				"completed", stats.Completed,
				"total", stats.Total,
				"success_rate", fmt.Sprintf("%.1f%%", stats.SuccessRate),
				"remaining", stats.Remaining,
				"eta", stats.ETA)

			// 批量更新进度 - 减少数据库写操作频率
			// 只在每完成10个任务或每5秒更新一次进度
			if stats.Completed%10 == 0 || time.Since(lastProgressUpdate) > 5*time.Second {
				go func(currentStats ProgressStats) {
					err := s.executeWithRetry(ctx, "update_batch_progress", func() error {
						return s.updateBatchOperationProgress(operationID, currentStats.Completed, currentStats.Success, currentStats.Failed)
					})
					if err != nil {
						s.logger.Error("更新批量操作进度失败",
							"operation_id", operationID,
							"completed", currentStats.Completed,
							"success", currentStats.Success,
							"failed", currentStats.Failed,
							"error", err)
					}
				}(stats)
				lastProgressUpdate = time.Now()
			}

			// 每10个结果或每30秒输出一次详细进度
			if completed%10 == 0 || time.Since(progressTracker.LastUpdate) > 30*time.Second {
				s.logger.Info("验证进度报告",
					"operation_id", operationID,
					"progress", fmt.Sprintf("%d/%d (%.1f%%)", stats.Completed, stats.Total, float64(stats.Completed)/float64(stats.Total)*100),
					"success_count", stats.Success,
					"failed_count", stats.Failed,
					"success_rate", fmt.Sprintf("%.1f%%", stats.SuccessRate),
					"elapsed", stats.Elapsed,
					"eta", stats.ETA,
					"active_slots", len(concurrencyMonitor.GetActiveSlots()))
			}

		case <-verifyCtx.Done():
			finalStats := progressTracker.GetStats()
			s.logger.Warn("验证任务超时",
				"operation_id", operationID,
				"completed", finalStats.Completed,
				"total", len(req.AccountEmails),
				"success", finalStats.Success,
				"failed", finalStats.Failed,
				"timeout_duration", time.Since(startTime))

			// 超时时也需要更新最终状态，避免无限循环
			completed = len(req.AccountEmails) // 强制退出循环
			break
		}
	}

	// 获取最终统计
	finalStats := progressTracker.GetStats()
	totalDuration := time.Since(startTime)

	s.logger.Info("验证任务结果收集完成",
		"operation_id", operationID,
		"total_duration", totalDuration,
		"completed", finalStats.Completed,
		"success", finalStats.Success,
		"failed", finalStats.Failed,
		"success_rate", fmt.Sprintf("%.1f%%", finalStats.SuccessRate))

	// 使用数据库重试机制完成操作
	completedAt := time.Now()

	// 根据实际完成情况决定最终状态
	finalStatus := "completed"
	if finalStats.Completed < len(req.AccountEmails) {
		finalStatus = "failed" // 如果没有完全完成，标记为失败
		s.logger.Warn("验证任务未完全完成",
			"operation_id", operationID,
			"completed", finalStats.Completed,
			"total", len(req.AccountEmails),
			"completion_rate", fmt.Sprintf("%.1f%%", float64(finalStats.Completed)/float64(len(req.AccountEmails))*100))
	}

	err = s.executeWithRetry(ctx, "update_batch_status_completed", func() error {
		return s.updateBatchOperationStatus(operationID, finalStatus, nil, &completedAt)
	})
	if err != nil {
		s.logger.Error("更新批量操作状态为完成失败",
			"operation_id", operationID,
			"final_status", finalStatus,
			"error", err)
	}

	err = s.executeWithRetry(ctx, "update_batch_counts", func() error {
		return s.updateBatchOperationCounts(operationID, len(req.AccountEmails), finalStats.Success, finalStats.Failed)
	})
	if err != nil {
		s.logger.Error("更新批量操作计数失败",
			"operation_id", operationID,
			"error", err)
	}

	s.logger.Info("验证任务完成",
		"operation_id", operationID,
		"total_duration", totalDuration,
		"total_accounts", len(req.AccountEmails),
		"success_count", finalStats.Success,
		"failed_count", finalStats.Failed,
		"success_rate", fmt.Sprintf("%.1f%%", finalStats.SuccessRate),
		"average_time_per_account", time.Duration(int64(totalDuration)/int64(len(req.AccountEmails))))
}

// executeVerificationTask 执行验证任务（异步优化版本）- 保留原方法以兼容其他调用
func (s *MailboxManagementService) executeVerificationTask(ctx context.Context, operationID string, req *database.VerificationTaskRequest) {
	s.logger.Info("开始执行验证任务", "operation_id", operationID, "account_count", len(req.AccountEmails))

	// 更新操作状态为运行中
	s.updateBatchOperationStatus(operationID, "running", &[]time.Time{time.Now()}[0], nil)

	var successCount, failedCount int

	// 创建信号量控制并发
	semaphore := make(chan struct{}, req.ConcurrentLimit)
	results := make(chan bool, len(req.AccountEmails))

	// 创建带超时的上下文 - 统一超时策略
	// 计算合理的总超时时间：单个任务30秒 + 并发缓冲
	totalTimeout := time.Duration(len(req.AccountEmails)*30/req.ConcurrentLimit+60) * time.Second
	if totalTimeout > 15*time.Minute {
		totalTimeout = 15 * time.Minute // 最大15分钟
	}
	verifyCtx, cancel := context.WithTimeout(ctx, totalTimeout)
	defer cancel()

	// 并发执行验证（使用带超时的上下文和goroutine泄漏保护）
	for _, email := range req.AccountEmails {
		go func(email string) {
			// 添加defer恢复机制，防止panic导致goroutine泄漏
			defer func() {
				if r := recover(); r != nil {
					s.logger.Error("验证goroutine发生panic", "email", email, "panic", r)
				}
			}()

			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			// 使用带超时的上下文进行验证
			success := s.verifyAccountWithTimeout(verifyCtx, email, req.VerificationType)

			// 非阻塞发送结果，增加超时保护
			select {
			case results <- success:
			case <-verifyCtx.Done():
				s.logger.Debug("验证任务因上下文取消而退出", "email", email)
				return // 超时退出
			case <-time.After(5 * time.Second):
				s.logger.Warn("发送验证结果超时", "email", email)
				return // 防止阻塞
			}
		}(email)
	}

	// 收集结果（带超时处理）
	completed := 0
	for completed < len(req.AccountEmails) {
		select {
		case success := <-results:
			completed++
			if success {
				successCount++
			} else {
				failedCount++
			}
			// 批量更新进度，减少数据库写操作
			if completed%5 == 0 { // 每完成5个任务更新一次
				go s.updateBatchOperationProgress(operationID, completed, successCount, failedCount)
			}

		case <-verifyCtx.Done():
			s.logger.Warn("验证任务超时", "operation_id", operationID, "completed", completed, "total", len(req.AccountEmails))
			// 超时时强制退出循环，避免无限等待
			completed = len(req.AccountEmails)
			break
		}
	}

	// 完成操作
	completedAt := time.Now()

	// 根据完成情况决定最终状态
	finalStatus := "completed"
	if completed < len(req.AccountEmails) {
		finalStatus = "failed" // 如果没有完全完成，标记为失败
		s.logger.Warn("验证任务未完全完成",
			"operation_id", operationID,
			"completed", completed,
			"total", len(req.AccountEmails))
	}

	s.updateBatchOperationStatus(operationID, finalStatus, nil, &completedAt)
	s.updateBatchOperationCounts(operationID, len(req.AccountEmails), successCount, failedCount)

	s.logger.Info("验证任务完成", "operation_id", operationID, "success", successCount, "failed", failedCount)
}

// verifyAccountWithTimeoutEnhanced 验证单个账户
func (s *MailboxManagementService) verifyAccountWithTimeoutEnhanced(ctx context.Context, verifyContext *VerificationContext, verificationType string) bool {
	// 创建单个账户验证的超时上下文
	accountCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	verifyContext.LogDebug("开始账户验证流程",
		"verification_type", verificationType,
		"timeout", "30秒")

	// 第一步：获取账户信息（使用数据库重试机制）
	var accountEmail, accountPassword string
	query := "SELECT email, password FROM accounts WHERE email = ?"

	verifyContext.LogDebug("查询账户信息",
		"query", query,
		"email", verifyContext.Email)

	err := s.executeWithRetry(accountCtx, "query_account_info", func() error {
		return s.db.GetDB().QueryRowContext(accountCtx, query, verifyContext.Email).Scan(&accountEmail, &accountPassword)
	})

	if err != nil {
		if err == sql.ErrNoRows {
			verifyContext.LogError("账户不存在", "error", err)
			s.updateAccountVerificationResultEnhanced(verifyContext, "failed", "账户不存在")
			return false
		}
		verifyContext.LogError("查询账户信息失败", "error", err)
		s.updateAccountVerificationResultEnhanced(verifyContext, "failed", "数据库查询失败: "+err.Error())
		return false
	}

	verifyContext.LogDebug("账户信息查询成功",
		"account_email", accountEmail,
		"password_length", len(accountPassword))

	// 第二步：创建账户对象进行验证
	verifyContext.LogDebug("创建账户验证对象")
	accountForVerify := types.Account{
		Username: accountEmail,
		Password: accountPassword,
		Status:   types.AccountStatusInactive,
	}

	// 第三步：执行登录验证
	verifyContext.LogInfo("开始执行登录验证")
	result, err := s.mailManager.BatchLogin(accountCtx, []types.Account{accountForVerify})
	if err != nil {
		verifyContext.LogError("登录验证失败", "error", err)
		s.updateAccountVerificationResultEnhanced(verifyContext, "failed", err.Error())
		return false
	}

	verifyContext.LogDebug("登录验证请求完成，检查结果")

	// 第四步：检查验证结果
	if loginResult, exists := result.Results[verifyContext.Email]; exists && loginResult.Success {
		verifyContext.LogInfo("账户验证成功",
			"login_message", loginResult.Message)
		s.updateAccountVerificationResultEnhanced(verifyContext, "verified", "验证成功")
		return true
	} else {
		errorMsg := "登录失败"
		if loginResult, exists := result.Results[verifyContext.Email]; exists {
			errorMsg = loginResult.Message
		}
		verifyContext.LogError("账户验证失败",
			"login_message", errorMsg,
			"result_exists", exists)
		s.updateAccountVerificationResultEnhanced(verifyContext, "failed", errorMsg)
		return false
	}
}

// verifyAccountWithTimeout 验证单个账户（带超时控制）
func (s *MailboxManagementService) verifyAccountWithTimeout(ctx context.Context, email, verificationType string) bool {
	// 创建单个账户验证的超时上下文
	accountCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	startTime := time.Now()

	// 获取账户信息（使用带超时的查询）
	var accountEmail, accountPassword string
	query := "SELECT email, password FROM accounts WHERE email = ?"

	// 使用带超时的查询
	done := make(chan error, 1)
	go func() {
		done <- s.db.GetDB().QueryRowContext(accountCtx, query, email).Scan(&accountEmail, &accountPassword)
	}()

	select {
	case err := <-done:
		if err != nil {
			s.updateAccountVerificationResult(email, "failed", "账户不存在", time.Since(startTime))
			return false
		}
	case <-accountCtx.Done():
		s.updateAccountVerificationResult(email, "failed", "查询超时", time.Since(startTime))
		return false
	}

	// 调用原有的验证逻辑
	return s.verifyAccount(accountCtx, email, verificationType)
}

// verifyAccount 验证单个账户
func (s *MailboxManagementService) verifyAccount(ctx context.Context, email, _ string) bool {
	startTime := time.Now()

	// 获取账户信息
	var accountEmail, accountPassword string
	query := "SELECT email, password FROM accounts WHERE email = ?"
	err := s.db.GetDB().QueryRowContext(ctx, query, email).Scan(&accountEmail, &accountPassword)
	if err != nil {
		s.updateAccountVerificationResult(email, "failed", "账户不存在", time.Since(startTime))
		return false
	}

	// 创建账户对象进行验证（使用明文密码）
	accountForVerify := types.Account{
		Username: email,
		Password: accountPassword,
		Status:   types.AccountStatusInactive,
	}

	// 执行登录验证
	result, err := s.mailManager.BatchLogin(ctx, []types.Account{accountForVerify})
	if err != nil {
		s.updateAccountVerificationResult(email, "failed", err.Error(), time.Since(startTime))
		return false
	}

	// 检查验证结果
	if loginResult, exists := result.Results[email]; exists && loginResult.Success {
		s.updateAccountVerificationResult(email, "verified", "验证成功", time.Since(startTime))
		return true
	} else {
		errorMsg := "登录失败"
		if loginResult, exists := result.Results[email]; exists {
			errorMsg = loginResult.Message
		}
		s.updateAccountVerificationResult(email, "failed", errorMsg, time.Since(startTime))
		return false
	}
}

// updateAccountVerificationResult 
func (s *MailboxManagementService) updateAccountVerificationResult(email, status, errorMsg string, duration time.Duration) {
	// 异步执行数据库更新，避免阻塞验证流程
	go func() {
		s.logger.Debug("开始更新账户验证结果",
			"email", email,
			"status", status,
			"error_message", errorMsg,
			"duration", duration)

		// 使用数据库重试机制执行更新
		err := s.executeWithRetry(context.Background(), "update_account_verification_result_legacy", func() error {
			query := `UPDATE accounts SET
				verification_status = ?,
				verification_error = ?,
				last_verification_time = ?,
				login_status = ?
				WHERE email = ?`

			// 优化：login_status 现在始终反映最新的验证状态
			loginStatus := "failed"
			if status == "verified" {
				loginStatus = "success"
			}

			_, err := s.db.GetDB().Exec(query, status, errorMsg, time.Now(), loginStatus, email)
			return err
		})

		if err != nil {
			s.logger.Error("更新账户验证结果失败",
				"email", email,
				"status", status,
				"error_message", errorMsg,
				"duration", duration,
				"update_error", err)
		} else {
			s.logger.Debug("账户验证结果更新成功",
				"email", email,
				"status", status,
				"duration", duration)
		}
	}()
}

// startTask 启动任务
func (s *MailboxManagementService) startTask(taskID string) error {
	query := "UPDATE task_scheduler_status SET status = 'running', updated_at = ? WHERE task_name = ?"
	_, err := s.db.GetDB().Exec(query, time.Now(), taskID)
	return err
}

// pauseTask 暂停任务
func (s *MailboxManagementService) pauseTask(taskID string) error {
	query := "UPDATE task_scheduler_status SET status = 'paused', updated_at = ? WHERE task_name = ?"
	_, err := s.db.GetDB().Exec(query, time.Now(), taskID)
	return err
}

// stopTask 停止任务
func (s *MailboxManagementService) stopTask(taskID string) error {
	query := "UPDATE task_scheduler_status SET status = 'stopped', updated_at = ? WHERE task_name = ?"
	_, err := s.db.GetDB().Exec(query, time.Now(), taskID)
	return err
}

// resetTask 重置任务
func (s *MailboxManagementService) resetTask(taskID string) error {
	query := `UPDATE task_scheduler_status SET 
		status = 'stopped', 
		current_operation_id = '', 
		run_count = 0, 
		error_count = 0, 
		last_error = '', 
		updated_at = ? 
		WHERE task_name = ?`
	_, err := s.db.GetDB().Exec(query, time.Now(), taskID)
	return err
}

// createBatchOperation 创建批量操作记录
func (s *MailboxManagementService) createBatchOperation(batchOp *database.BatchOperation) error {
	query := `INSERT INTO batch_operations (
		operation_id, operation_type, status, total_count,
		processed_count, success_count, failed_count,
		created_by, created_at, operation_params
	) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	paramsJSON, _ := json.Marshal(batchOp.OperationParams)

	_, err := s.db.GetDB().Exec(query,
		batchOp.OperationID, batchOp.OperationType, batchOp.Status, batchOp.TotalCount,
		0, 0, 0, // 初始计数为0
		batchOp.CreatedBy, batchOp.CreatedAt, string(paramsJSON))

	return err
}

// executeBatchDelete 执行批量删除
func (s *MailboxManagementService) executeBatchDelete(ctx context.Context, operationID string, accountIDs []int) {
	s.logger.Info("开始执行批量删除任务", "operation_id", operationID, "account_count", len(accountIDs))

	// 更新操作状态为运行中
	s.updateBatchOperationStatus(operationID, "running", &[]time.Time{time.Now()}[0], nil)

	var successCount, failedCount int

	for _, accountID := range accountIDs {
		err := s.DeleteAccount(ctx, fmt.Sprintf("%d", accountID))
		if err != nil {
			s.logger.Error("删除账户失败", "account_id", accountID, "error", err)
			failedCount++
		} else {
			successCount++
		}

		// 更新进度
		s.updateBatchOperationProgress(operationID, successCount+failedCount, successCount, failedCount)
	}

	// 完成操作
	completedAt := time.Now()
	s.updateBatchOperationStatus(operationID, "completed", nil, &completedAt)
	s.updateBatchOperationCounts(operationID, len(accountIDs), successCount, failedCount)

	s.logger.Info("批量删除任务完成", "operation_id", operationID, "success", successCount, "failed", failedCount)
}

// executeBatchDisable 执行批量禁用
func (s *MailboxManagementService) executeBatchDisable(ctx context.Context, operationID string, accountIDs []int) {
	s.logger.Info("开始执行批量禁用任务", "operation_id", operationID, "account_count", len(accountIDs))

	// 更新操作状态为运行中
	s.updateBatchOperationStatus(operationID, "running", &[]time.Time{time.Now()}[0], nil)

	var successCount, failedCount int

	for _, accountID := range accountIDs {
		err := s.ToggleAccountStatus(ctx, fmt.Sprintf("%d", accountID), true) // true表示禁用
		if err != nil {
			s.logger.Error("禁用账户失败", "account_id", accountID, "error", err)
			failedCount++
		} else {
			successCount++
		}

		// 更新进度
		s.updateBatchOperationProgress(operationID, successCount+failedCount, successCount, failedCount)
	}

	// 完成操作
	completedAt := time.Now()
	s.updateBatchOperationStatus(operationID, "completed", nil, &completedAt)
	s.updateBatchOperationCounts(operationID, len(accountIDs), successCount, failedCount)

	s.logger.Info("批量禁用任务完成", "operation_id", operationID, "success", successCount, "failed", failedCount)
}

// updateAccountVerificationResultEnhanced 更新账户验证结果
func (s *MailboxManagementService) updateAccountVerificationResultEnhanced(verifyContext *VerificationContext, status, errorMsg string) {
	verifyContext.LogDebug("开始更新账户验证结果",
		"status", status,
		"error_message", errorMsg)

	// 使用数据库重试机制执行更新
	err := s.executeWithRetry(context.Background(), "update_account_verification_result", func() error {
		query := `UPDATE accounts SET
			verification_status = ?,
			verification_error = ?,
			last_verification_time = ?,
			login_status = ?
			WHERE email = ?`

		// 优化：login_status 现在始终反映最新的验证状态
		loginStatus := "failed"
		if status == "verified" {
			loginStatus = "success"
		}

		_, err := s.db.GetDB().Exec(query, status, errorMsg, time.Now(), loginStatus, verifyContext.Email)
		return err
	})

	if err != nil {
		verifyContext.LogError("更新账户验证结果失败",
			"status", status,
			"error_message", errorMsg,
			"update_error", err)
	} else {
		verifyContext.LogDebug("账户验证结果更新成功",
			"status", status,
			"login_status", func() string {
				if status == "verified" {
					return "success"
				}
				return "failed"
			}())
	}
}

import request from '@/utils/request'
import type { ApiResponse, PaginatedResponse } from '@/types/api'

// 邮箱管理相关类型定义
export interface ExtendedAccount {
  id: number
  email: string
  login_status: string
  last_login_time?: string
  email_status: string
  usage_status: string
  created_at: string
  updated_at: string
  batch_import_id?: string
  verification_status: string
  last_verification_time?: string
  verification_error?: string
  is_disabled: boolean
  import_source: string
  tags: string[]
}

export interface BatchImportRequest {
  accounts: AccountImportData[]
  source: string
  tags: string[]
  auto_verify: boolean
  description: string
}

export interface AccountImportData {
  email: string
  password: string
}

export interface BatchOperationResponse {
  operation_id: string
  status: string
  message: string
  total_count: number
}

export interface BatchOperation {
  id: number
  operation_id: string
  operation_type: string
  status: string
  total_count: number
  processed_count: number
  success_count: number
  failed_count: number
  error_message?: string
  created_by: string
  created_at: string
  started_at?: string
  completed_at?: string
  progress_data?: Record<string, any>
  operation_params?: Record<string, any>
}

export interface VerificationTaskRequest {
  account_emails: string[]
  verification_type: string
  concurrent_limit: number
  retry_limit: number
}

export interface TaskControlRequest {
  action: string // start, pause, stop, reset
  task_id: string
}

export interface TaskSchedulerStatus {
  id: number
  task_name: string
  task_type: string
  status: string
  current_operation_id?: string
  last_run_at?: string
  next_run_at?: string
  run_count: number
  error_count: number
  last_error?: string
  config_data?: Record<string, any>
  created_at: string
  updated_at: string
}

export interface MailboxStatistics {
  id: number
  stat_date: string
  total_accounts: number
  active_accounts: number
  verified_accounts: number
  failed_accounts: number
  disabled_accounts: number
  new_imports_today: number
  verification_success_rate: number
  avg_verification_time_ms: number
  created_at: string
}

// {{ AURA-X: Add - 添加代理配置类型定义. Principle: SOLID. Approval: 寸止(ID:proxy-config). }}
export interface ProxyConfigData {
  enabled: boolean
  proxy_url: string
  proxy_type: string
}

export interface ProxyTestResult {
  success: boolean
  ip?: string
  country?: string
  city?: string
  region?: string
  isp?: string
  error?: string
  latency_ms?: number
}

export interface MailboxFilterRequest {
  status?: string[]
  verification_status?: string[]
  import_source?: string[]
  tags?: string[]
  date_range?: {
    start_date: string
    end_date: string
  }
  page: number
  page_size: number
  sort_by?: string
  sort_order?: string
}

// API 接口函数
export const mailboxApi = {
  // 批量导入邮箱账户
  batchImportAccounts(data: BatchImportRequest): Promise<ApiResponse<BatchOperationResponse>> {
    return request.post('/mailbox/batch-import', data)
  },

  // 获取账户列表
  getAccountsList(
    params: MailboxFilterRequest
  ): Promise<ApiResponse<PaginatedResponse<ExtendedAccount>>> {
    return request.get('/mailbox/accounts', { params })
  },

  // 启动验证任务
  startVerificationTask(
    data: VerificationTaskRequest
  ): Promise<ApiResponse<BatchOperationResponse>> {
    return request.post('/mailbox/verify', data)
  },

  // 获取批量操作状态
  getBatchOperationStatus(operationId: string): Promise<ApiResponse<BatchOperation>> {
    return request.get(`/mailbox/batch-operation/${operationId}`)
  },

  // 控制任务
  controlTask(data: TaskControlRequest): Promise<ApiResponse<void>> {
    return request.post('/mailbox/task-control', data)
  },

  // 获取任务调度器状态
  getTaskSchedulerStatus(): Promise<ApiResponse<TaskSchedulerStatus[]>> {
    return request.get('/mailbox/scheduler-status')
  },

  // 获取邮箱统计信息
  getMailboxStatistics(): Promise<ApiResponse<MailboxStatistics>> {
    return request.get('/mailbox/statistics')
  },

  // 删除账户
  deleteAccount(accountId: number): Promise<ApiResponse<void>> {
    return request.delete(`/mailbox/accounts/${accountId}`)
  },

  // 批量删除账户
  batchDeleteAccounts(accountIds: number[]): Promise<ApiResponse<BatchOperationResponse>> {
    return request.post('/mailbox/batch-delete', { account_ids: accountIds })
  },

  // 禁用/启用账户
  toggleAccountStatus(accountId: number, disabled: boolean): Promise<ApiResponse<void>> {
    return request.put(`/mailbox/accounts/${accountId}/status`, { is_disabled: disabled })
  },

  // 批量禁用账户
  batchDisableAccounts(accountIds: number[]): Promise<ApiResponse<BatchOperationResponse>> {
    return request.post('/mailbox/batch-disable', { account_ids: accountIds })
  },

  // 解析批量导入文本
  parseBatchImportText(text: string): AccountImportData[] {
    const lines = text.split('\n').filter(line => line.trim())
    const accounts: AccountImportData[] = []

    for (const line of lines) {
      const trimmedLine = line.trim()
      if (!trimmedLine) continue

      // 支持多种分隔符格式
      const separators = ['----', '---', '--', '\t', ' ']
      let email = ''
      let password = ''

      for (const separator of separators) {
        if (trimmedLine.includes(separator)) {
          const parts = trimmedLine.split(separator)
          if (parts.length >= 2) {
            email = parts[0].trim()
            password = parts[1].trim()
            break
          }
        }
      }

      // 验证邮箱格式
      if (email && password && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        accounts.push({ email, password })
      }
    }

    return accounts
  },

  // 验证邮箱格式
  validateEmail(email: string): boolean {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
  },

  // 格式化导入预览
  formatImportPreview(accounts: AccountImportData[]): {
    valid: AccountImportData[]
    invalid: string[]
    summary: {
      total: number
      valid: number
      invalid: number
    }
  } {
    const valid: AccountImportData[] = []
    const invalid: string[] = []

    accounts.forEach(account => {
      if (this.validateEmail(account.email) && account.password) {
        valid.push(account)
      } else {
        invalid.push(`${account.email} - 格式错误`)
      }
    })

    return {
      valid,
      invalid,
      summary: {
        total: accounts.length,
        valid: valid.length,
        invalid: invalid.length,
      },
    }
  },

  // 获取状态选项
  getStatusOptions() {
    return [
      { label: '全部状态', value: '' },
      { label: '登录成功', value: 'success' },
      { label: '登录失败', value: 'failed' },
      { label: '未登录', value: 'pending' },
    ]
  },

  // 获取验证状态选项
  getVerificationStatusOptions() {
    return [
      { label: '全部状态', value: '' },
      { label: '未验证', value: 'unverified' },
      { label: '验证成功', value: 'verified' },
      { label: '验证失败', value: 'failed' },
    ]
  },

  // 获取导入来源选项
  getImportSourceOptions() {
    return [
      { label: '全部来源', value: '' },
      { label: '手动添加', value: 'manual' },
      { label: '批量导入', value: 'batch_import' },
      { label: 'API导入', value: 'api' },
    ]
  },

  // {{ AURA-X: Add - 添加代理配置API方法. Principle: SOLID. Approval: 寸止(ID:proxy-config). }}
  // 获取代理配置
  getProxyConfig(): Promise<ApiResponse<ProxyConfigData>> {
    return request.get('/mailbox/proxy-config')
  },

  // 设置代理配置
  setProxyConfig(config: ProxyConfigData): Promise<ApiResponse<void>> {
    return request.post('/mailbox/proxy-config', config)
  },

  // 删除代理配置
  deleteProxyConfig(): Promise<ApiResponse<void>> {
    return request.delete('/mailbox/proxy-config')
  },

  // 测试代理配置
  testProxyConfig(config: ProxyConfigData): Promise<ApiResponse<ProxyTestResult>> {
    return request.post('/mailbox/proxy-config/test', config)
  },

  // 格式化状态显示
  formatStatus(status: string): { text: string; type: 'success' | 'error' | 'warning' | 'info' } {
    const statusMap: Record<
      string,
      { text: string; type: 'success' | 'error' | 'warning' | 'info' }
    > = {
      success: { text: '成功', type: 'success' },
      failed: { text: '失败', type: 'error' },
      pending: { text: '未登录', type: 'warning' },
      verified: { text: '验证成功', type: 'success' },
      unverified: { text: '未验证', type: 'info' },
      active: { text: '活跃', type: 'success' },
      inactive: { text: '非活跃', type: 'warning' },
      disabled: { text: '已禁用', type: 'error' },
    }

    return statusMap[status] || { text: status, type: 'info' }
  },

  // 格式化登录状态显示（带时间信息）
  formatLoginStatus(account: ExtendedAccount): {
    text: string
    type: 'success' | 'error' | 'warning' | 'info'
    tooltip?: string
  } {
    const baseStatus = this.formatStatus(account.login_status)

    if (account.last_login_time) {
      const lastLoginTime = new Date(account.last_login_time).toLocaleString()
      return {
        ...baseStatus,
        text: `${baseStatus.text}`,
        tooltip: `最后登录: ${lastLoginTime}`,
      }
    }

    return baseStatus
  },

  // 格式化验证状态显示（带时间和错误信息）
  formatVerificationStatus(account: ExtendedAccount): {
    text: string
    type: 'success' | 'error' | 'warning' | 'info'
    tooltip?: string
  } {
    const baseStatus = this.formatStatus(account.verification_status)

    let tooltip = ''
    if (account.last_verification_time) {
      const lastVerificationTime = new Date(account.last_verification_time).toLocaleString()
      tooltip = `最后验证: ${lastVerificationTime}`
    }

    if (account.verification_error && account.verification_status === 'failed') {
      tooltip += tooltip
        ? `\n错误信息: ${account.verification_error}`
        : `错误信息: ${account.verification_error}`
    }

    return {
      ...baseStatus,
      tooltip: tooltip || undefined,
    }
  },
}

export default mailboxApi

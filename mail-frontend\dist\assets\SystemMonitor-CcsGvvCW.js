import{s as v}from"./system-F2rQGnke.js";import{u as ee,F as se,B as T,h as U,v as u,L as P,M as g,I as te,y as ae,J as oe,w as le}from"./ui-DUh7fRR5.js";import{k as $,W as c,Q as i,X as e,r as y,e as B,c as ne,m as p,o as re,E as ie,S as o,R as r,K as a,H as I,$ as n,j as h,F as b,O as ce,a2 as K,a4 as de}from"./vendor-RHijBMdK.js";import{S as ue}from"./Settings-CsD-lPn5.js";import{R as q}from"./Refresh-BNivSboa.js";import{_ as me}from"./index-hmWk46K2.js";const _e={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},pe=e("path",{d:"M133 440a35.37 35.37 0 0 1-17.5-4.67c-12-6.8-19.46-20-19.46-34.33V111c0-14.37 7.46-27.53 19.46-34.33a35.13 35.13 0 0 1 35.77.45l247.85 148.36a36 36 0 0 1 0 61l-247.89 148.4A35.5 35.5 0 0 1 133 440z",fill:"currentColor"},null,-1),ve=[pe],ye=$({name:"Play",render:function(d,f){return i(),c("svg",_e,ve)}}),fe={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},ge=e("path",{d:"M392 432H120a40 40 0 0 1-40-40V120a40 40 0 0 1 40-40h272a40 40 0 0 1 40 40v272a40 40 0 0 1-40 40z",fill:"currentColor"},null,-1),he=[ge],be=$({name:"Stop",render:function(d,f){return i(),c("svg",fe,he)}}),ke={class:"system-monitor"},we={class:"status-grid"},xe={class:"status-item"},Se={class:"status-info"},Te={class:"status-label"},Ue={class:"status-uptime"},Be={class:"metric-item"},Ce={class:"metric-item"},Me={class:"metric-detail"},ze={class:"metric-item"},Ne={class:"metric-detail"},Re={class:"metric-item"},Fe={class:"database-info"},Le={class:"db-size"},Pe={class:"value"},Ie={class:"work-dir-size"},$e={class:"value"},De={class:"metric-item"},Oe={class:"memory-details"},Ve={class:"memory-item"},Ae={class:"value"},Ee={class:"memory-item"},Ke={class:"value"},qe={class:"memory-item"},Qe={class:"value"},We={class:"memory-item"},He={class:"value"},je={class:"metric-item"},Ge={key:1,class:"database-status"},Je={class:"status-text"},Xe={class:"db-metrics"},Ye={class:"db-metric"},Ze={class:"value"},es={class:"db-metric"},ss={class:"value"},ts={class:"metric-item"},as={key:1,class:"table-counts"},os={class:"table-name"},ls={class:"table-count"},ns={class:"metric-item"},rs={class:"performance-metrics"},is={class:"perf-metric"},cs={class:"value"},ds={class:"perf-metric"},us={class:"value"},ms={class:"perf-metric"},_s={class:"value"},ps={class:"logs-container"},vs={class:"log-time"},ys={class:"log-level"},fs={class:"log-message"},gs={key:0,class:"empty-logs"},hs=$({__name:"SystemMonitor",setup(D){const d=ee(),f=y(!1),k=y(!1),C=y(!1),O=y(!1),M=y("info"),V=y([]),z=y([]),w=B({status:"healthy",label:"运行正常",uptime:"0天0小时0分钟"}),l=B({memoryUsage:65,appMemoryUsage:"0 B",memoryTotal:"0 B",memoryUsed:"0 B",memoryPercent:0,cpuUsage:32,cpuCores:0,diskUsage:"0 B",diskTotal:"0 B",diskUsed:"0 B",diskPercent:0,databaseSize:"0 B",workDirSize:"0 B"}),m=B({size:"0 B",tableCounts:{},connectionOK:!1,avgQueryTime:0}),N=B({avgResponseTime:0,totalRequests:0,errorRate:0,activeSessions:0}),Q=[{label:"全部",value:""},{label:"DEBUG",value:"debug"},{label:"INFO",value:"info"},{label:"WARN",value:"warn"},{label:"ERROR",value:"error"}];let x=null;const W=ne(()=>[{title:"任务名称",key:"name",width:200},{title:"状态",key:"status",width:100,render:s=>{let t;return s.enabled?s.is_running?t={label:"运行中",type:"success"}:s.last_error?t={label:"错误",type:"error"}:t={label:"等待中",type:"warning"}:t={label:"已禁用",type:"default"},p(se,{type:t.type},{default:()=>t.label})}},{title:"上次运行",key:"last_run",width:180,render:s=>s.last_run?F(s.last_run):"-"},{title:"下次运行",key:"next_run",width:180,render:s=>s.next_run?F(s.next_run):"-"},{title:"运行次数",key:"run_count",width:100},{title:"错误次数",key:"error_count",width:100,render:s=>p("span",{style:{color:s.error_count>0?"#d03050":"#18a058"}},s.error_count)},{title:"操作",key:"actions",width:150,render:s=>p("div",{class:"action-buttons"},[p(T,{size:"small",type:s.enabled?"error":"primary",quaternary:!0,onClick:()=>G(s)},{icon:()=>p(U,null,{default:()=>p(s.enabled?be:ye)}),default:()=>s.enabled?"禁用":"启用"}),p(T,{size:"small",quaternary:!0,onClick:()=>J(s)},{icon:()=>p(U,null,{default:()=>p(ue)}),default:()=>"执行"})])}]),R=s=>s<50?"#18a058":s<80?"#f0a020":"#d03050",F=s=>new Date(s).toLocaleString(),A=async()=>{await H(),setTimeout(()=>{j()},100)},H=async()=>{f.value=!0;try{const s=await v.getSystemStatsWithMode("core");if(s.success&&s.data){const t=s.data.system;t&&(l.memoryUsage=t.memory_percent||0,l.appMemoryUsage=t.app_memory_usage||"0 B",l.memoryTotal=t.memory_total||"0 B",l.memoryUsed=t.memory_used||"0 B",l.memoryPercent=t.memory_percent||0,l.cpuUsage=t.cpu_usage||0,l.cpuCores=t.cpu_cores||0,w.uptime=t.uptime||"0天0小时0分钟")}}catch(s){console.error("Failed to fetch core metrics:",s),d.error("获取核心指标失败")}finally{f.value=!1}},j=async()=>{k.value=!0;try{const s=await v.getSystemStatsWithMode("database");if(s.success&&s.data){const t=s.data.database;t&&(m.size=t.size||"0 B",m.tableCounts=t.table_counts||{},m.connectionOK=t.connection_ok||!1,m.avgQueryTime=t.avg_query_time_ms||0)}}catch(s){console.error("Failed to fetch database metrics:",s),d.error("获取数据库统计失败")}finally{k.value=!1}},S=async()=>{try{C.value=!0;const s=await v.getTasks();s.success&&s.data&&(V.value=s.data)}catch(s){console.error("Failed to fetch tasks:",s),d.error("获取任务列表失败")}finally{C.value=!1}},L=async()=>{try{O.value=!0;const s=await v.getSystemLogs({level:M.value||void 0,page:1,pageSize:100});s.success&&s.data&&(z.value=s.data.Items||s.data.items||[])}catch(s){console.error("Failed to fetch logs:",s),d.error("获取系统日志失败")}finally{O.value=!1}},G=async s=>{try{s.enabled?(await v.disableTask(s.name),d.success(`任务 ${s.name} 已禁用`)):(await v.enableTask(s.name),d.success(`任务 ${s.name} 已启用`)),await S()}catch(t){console.error("Failed to toggle task:",t),d.error("操作失败")}},J=async s=>{try{await v.runTask(s.name),d.success(`任务 ${s.name} 已手动执行`),await S()}catch(t){console.error("Failed to run task:",t),d.error("执行失败")}},X=()=>{S()},Y=()=>{x=setInterval(()=>{A()},3e4)},Z=()=>{x&&(clearInterval(x),x=null)};return re(()=>{A(),S(),L(),Y()}),ie(()=>{Z()}),(s,t)=>(i(),c("div",ke,[t[14]||(t[14]=e("div",{class:"page-header"},[e("h1",null,"系统监控"),e("p",null,"实时监控系统运行状态和性能指标")],-1)),e("div",we,[o(a(u),{title:"系统状态",class:"status-card"},{default:r(()=>[e("div",xe,[e("div",{class:I(["status-indicator",w.status])},null,2),e("div",Se,[e("div",Te,n(w.label),1),e("div",Ue,"运行时间: "+n(w.uptime),1)])])]),_:1}),o(a(u),{title:"内存使用",class:"status-card"},{default:r(()=>[e("div",Be,[o(a(P),{type:"circle",percentage:l.memoryUsage,color:R(l.memoryUsage)},{default:r(()=>[h(n(l.memoryUsage)+"% ",1)]),_:1},8,["percentage","color"])])]),_:1}),o(a(u),{title:"CPU使用率",class:"status-card"},{default:r(()=>[e("div",Ce,[f.value?(i(),c(b,{key:0},[o(a(g),{height:"80px",circle:""}),o(a(g),{text:"",repeat:2})],64)):(i(),c(b,{key:1},[o(a(P),{type:"circle",percentage:l.cpuUsage,color:R(l.cpuUsage)},{default:r(()=>[h(n(l.cpuUsage)+"% ",1)]),_:1},8,["percentage","color"]),e("div",Me,[e("div",null,"核心数: "+n(l.cpuCores),1)])],64))])]),_:1}),o(a(u),{title:"磁盘使用",class:"status-card"},{default:r(()=>[e("div",ze,[o(a(P),{type:"circle",percentage:l.diskPercent,color:R(l.diskPercent)},{default:r(()=>[h(n(l.diskPercent)+"% ",1)]),_:1},8,["percentage","color"]),e("div",Ne,[e("div",null,"已用: "+n(l.diskUsed),1),e("div",null,"总计: "+n(l.diskTotal),1)])])]),_:1}),o(a(u),{title:"数据库",class:"status-card"},{default:r(()=>[e("div",Re,[e("div",Fe,[e("div",Le,[t[1]||(t[1]=e("div",{class:"label"},"数据库大小",-1)),e("div",Pe,n(l.databaseSize),1)]),e("div",Ie,[t[2]||(t[2]=e("div",{class:"label"},"工作目录",-1)),e("div",$e,n(l.workDirSize),1)])])])]),_:1}),o(a(u),{title:"内存详情",class:"status-card"},{default:r(()=>[e("div",De,[e("div",Oe,[e("div",Ve,[t[3]||(t[3]=e("span",{class:"label"},"应用占用:",-1)),e("span",Ae,n(l.appMemoryUsage),1)]),e("div",Ee,[t[4]||(t[4]=e("span",{class:"label"},"系统已用:",-1)),e("span",Ke,n(l.memoryUsed),1)]),e("div",qe,[t[5]||(t[5]=e("span",{class:"label"},"系统总计:",-1)),e("span",Qe,n(l.memoryTotal),1)]),e("div",We,[t[6]||(t[6]=e("span",{class:"label"},"使用率:",-1)),e("span",He,n(l.memoryPercent)+"%",1)])])])]),_:1}),o(a(u),{title:"数据库状态",class:"status-card"},{default:r(()=>[e("div",je,[k.value?(i(),c(b,{key:0},[o(a(g),{height:"20px",width:"60%"}),o(a(g),{text:"",repeat:3})],64)):(i(),c("div",Ge,[e("div",{class:I(["status-indicator",{error:!m.connectionOK}])},null,2),e("div",Je,n(m.connectionOK?"连接正常":"连接异常"),1),e("div",Xe,[e("div",Ye,[t[7]||(t[7]=e("span",{class:"label"},"大小:",-1)),e("span",Ze,n(m.size),1)]),e("div",es,[t[8]||(t[8]=e("span",{class:"label"},"查询时间:",-1)),e("span",ss,n(m.avgQueryTime.toFixed(2))+"ms",1)])])]))])]),_:1}),o(a(u),{title:"表记录统计",class:"status-card"},{default:r(()=>[e("div",ts,[k.value?(i(),ce(a(g),{key:0,text:"",repeat:5})):(i(),c("div",as,[(i(!0),c(b,null,K(m.tableCounts,(_,E)=>(i(),c("div",{key:E,class:"table-count-item"},[e("span",os,n(E)+":",1),e("span",ls,n(_.toLocaleString()),1)]))),128))]))])]),_:1}),o(a(u),{title:"性能指标",class:"status-card"},{default:r(()=>[e("div",ns,[e("div",rs,[e("div",is,[t[9]||(t[9]=e("span",{class:"label"},"活跃会话:",-1)),e("span",cs,n(N.activeSessions),1)]),e("div",ds,[t[10]||(t[10]=e("span",{class:"label"},"响应时间:",-1)),e("span",us,n(N.avgResponseTime.toFixed(2))+"ms",1)]),e("div",ms,[t[11]||(t[11]=e("span",{class:"label"},"错误率:",-1)),e("span",_s,n(N.errorRate.toFixed(2))+"%",1)])])])]),_:1})]),o(a(u),{title:"定时任务状态",class:"tasks-card"},{"header-extra":r(()=>[o(a(T),{onClick:X},{icon:r(()=>[o(a(U),null,{default:r(()=>[o(a(q))]),_:1})]),default:r(()=>[t[12]||(t[12]=h(" 刷新 "))]),_:1,__:[12]})]),default:r(()=>[o(a(te),{columns:W.value,data:V.value,loading:C.value,pagination:!1},null,8,["columns","data","loading"])]),_:1}),o(a(u),{title:"系统日志",class:"logs-card"},{"header-extra":r(()=>[o(a(oe),null,{default:r(()=>[o(a(le),{value:M.value,"onUpdate:value":[t[0]||(t[0]=_=>M.value=_),L],options:Q,placeholder:"日志级别",style:{width:"120px"}},null,8,["value"]),o(a(T),{onClick:L},{icon:r(()=>[o(a(U),null,{default:r(()=>[o(a(q))]),_:1})]),default:r(()=>[t[13]||(t[13]=h(" 刷新 "))]),_:1,__:[13]})]),_:1})]),default:r(()=>[e("div",ps,[(i(!0),c(b,null,K(z.value,_=>(i(),c("div",{key:_.id,class:I(["log-item",_.level])},[e("span",vs,n(F(_.timestamp)),1),e("span",ys,n(_.level.toUpperCase()),1),e("span",fs,n(_.message),1)],2))),128)),z.value.length===0?(i(),c("div",gs,[o(a(ae),{description:"暂无日志记录"})])):de("",!0)])]),_:1})]))}}),Us=me(hs,[["__scopeId","data-v-00748a93"]]);export{Us as default};

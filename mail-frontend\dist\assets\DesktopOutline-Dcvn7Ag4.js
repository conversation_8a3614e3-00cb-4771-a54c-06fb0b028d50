import{k as e,W as t,Q as n,X as o}from"./vendor-RHijBMdK.js";const r={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},l=o("rect",{x:"32",y:"64",width:"448",height:"320",rx:"32",ry:"32",fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"32"},null,-1),s=o("path",{stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M304 448l-8-64h-80l-8 64h96z",fill:"currentColor"},null,-1),i=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M368 448H144"},null,-1),c=o("path",{d:"M32 304v48a32.09 32.09 0 0 0 32 32h384a32.09 32.09 0 0 0 32-32v-48zm224 64a16 16 0 1 1 16-16a16 16 0 0 1-16 16z",fill:"currentColor"},null,-1),d=[l,s,i,c],_=e({name:"DesktopOutline",render:function(a,k){return n(),t("svg",r,d)}});export{_ as M};

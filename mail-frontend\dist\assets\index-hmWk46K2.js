const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Login-BLVqzNxm.js","assets/vendor-RHijBMdK.js","assets/ui-DUh7fRR5.js","assets/Mail-DFX8DOvB.js","assets/Person-Cj8AC7xs.js","assets/Login-Byys5LJ9.css","assets/MainLayout-WDUUpJVe.js","assets/SpeedometerOutline-D-Vl8HvQ.js","assets/Key-DZgkvFqF.js","assets/DesktopOutline-Dcvn7Ag4.js","assets/Settings-CsD-lPn5.js","assets/Moon-V5Og8Ghg.js","assets/MainLayout-CftSm0Fs.css","assets/Dashboard-DJX5NdR2.js","assets/charts-C1YvGDjw.js","assets/system-F2rQGnke.js","assets/Add-DKKcgvRl.js","assets/Refresh-BNivSboa.js","assets/Dashboard-CNq0_iSK.css","assets/ActivationCodes-D_RFjFBM.js","assets/ActivationCodes-CW-7UUoN.css","assets/SystemMonitor-CcsGvvCW.js","assets/SystemMonitor-DNOmt7Vl.css","assets/SystemConfig-CQ3NDQ5w.js","assets/SystemConfig-7JWaCq-U.css","assets/MailboxManagement-C4pHxqVU.js","assets/MailboxManagement-CWcHAJ32.css","assets/Profile-BfX1Q-9j.js","assets/Profile-CjshSscm.css","assets/NotFound-CmmQpdft.js","assets/NotFound-CGhlgUgJ.css"])))=>i.map(i=>d[i]);
import{L as qe,r as x,c as Y,M as ht,N as mt,k as gt,o as yt,O as wt,K as L,P as bt,Q as St,R as $,S as q,U as Et,V as Rt}from"./vendor-RHijBMdK.js";import{d as At,a as Tt,z as Ot,N as _t,b as vt,c as Ct,e as xt,f as Pt,g as Nt}from"./ui-DUh7fRR5.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const s of o)if(s.type==="childList")for(const i of s.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(o){const s={};return o.integrity&&(s.integrity=o.integrity),o.referrerPolicy&&(s.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?s.credentials="include":o.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(o){if(o.ep)return;o.ep=!0;const s=n(o);fetch(o.href,s)}})();const kt="modulepreload",Lt=function(e){return"/"+e},Oe={},N=function(t,n,r){let o=Promise.resolve();if(n&&n.length>0){let i=function(u){return Promise.all(u.map(f=>Promise.resolve(f).then(p=>({status:"fulfilled",value:p}),p=>({status:"rejected",reason:p}))))};document.getElementsByTagName("link");const c=document.querySelector("meta[property=csp-nonce]"),d=(c==null?void 0:c.nonce)||(c==null?void 0:c.getAttribute("nonce"));o=i(n.map(u=>{if(u=Lt(u),u in Oe)return;Oe[u]=!0;const f=u.endsWith(".css"),p=f?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${u}"]${p}`))return;const y=document.createElement("link");if(y.rel=f?"stylesheet":kt,f||(y.as="script"),y.crossOrigin="",y.href=u,d&&y.setAttribute("nonce",d),document.head.appendChild(y),f)return new Promise((b,l)=>{y.addEventListener("load",b),y.addEventListener("error",()=>l(new Error(`Unable to preload CSS for ${u}`)))})}))}function s(i){const c=new Event("vite:preloadError",{cancelable:!0});if(c.payload=i,window.dispatchEvent(c),!c.defaultPrevented)throw i}return o.then(i=>{for(const c of i||[])c.status==="rejected"&&s(c.reason);return t().catch(s)})};function je(e,t){return function(){return e.apply(t,arguments)}}const{toString:Dt}=Object.prototype,{getPrototypeOf:be}=Object,{iterator:ne,toStringTag:Me}=Symbol,re=(e=>t=>{const n=Dt.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),v=e=>(e=e.toLowerCase(),t=>re(t)===e),oe=e=>t=>typeof t===e,{isArray:M}=Array,J=oe("undefined");function Ft(e){return e!==null&&!J(e)&&e.constructor!==null&&!J(e.constructor)&&O(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const ze=v("ArrayBuffer");function It(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&ze(e.buffer),t}const Ut=oe("string"),O=oe("function"),$e=oe("number"),se=e=>e!==null&&typeof e=="object",Bt=e=>e===!0||e===!1,X=e=>{if(re(e)!=="object")return!1;const t=be(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Me in e)&&!(ne in e)},qt=v("Date"),jt=v("File"),Mt=v("Blob"),zt=v("FileList"),$t=e=>se(e)&&O(e.pipe),Ht=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||O(e.append)&&((t=re(e))==="formdata"||t==="object"&&O(e.toString)&&e.toString()==="[object FormData]"))},Vt=v("URLSearchParams"),[Jt,Wt,Kt,Gt]=["ReadableStream","Request","Response","Headers"].map(v),Xt=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function W(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,o;if(typeof e!="object"&&(e=[e]),M(e))for(r=0,o=e.length;r<o;r++)t.call(null,e[r],r,e);else{const s=n?Object.getOwnPropertyNames(e):Object.keys(e),i=s.length;let c;for(r=0;r<i;r++)c=s[r],t.call(null,e[c],c,e)}}function He(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,o;for(;r-- >0;)if(o=n[r],t===o.toLowerCase())return o;return null}const I=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Ve=e=>!J(e)&&e!==I;function pe(){const{caseless:e}=Ve(this)&&this||{},t={},n=(r,o)=>{const s=e&&He(t,o)||o;X(t[s])&&X(r)?t[s]=pe(t[s],r):X(r)?t[s]=pe({},r):M(r)?t[s]=r.slice():t[s]=r};for(let r=0,o=arguments.length;r<o;r++)arguments[r]&&W(arguments[r],n);return t}const Qt=(e,t,n,{allOwnKeys:r}={})=>(W(t,(o,s)=>{n&&O(o)?e[s]=je(o,n):e[s]=o},{allOwnKeys:r}),e),Zt=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Yt=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},en=(e,t,n,r)=>{let o,s,i;const c={};if(t=t||{},e==null)return t;do{for(o=Object.getOwnPropertyNames(e),s=o.length;s-- >0;)i=o[s],(!r||r(i,e,t))&&!c[i]&&(t[i]=e[i],c[i]=!0);e=n!==!1&&be(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},tn=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},nn=e=>{if(!e)return null;if(M(e))return e;let t=e.length;if(!$e(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},rn=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&be(Uint8Array)),on=(e,t)=>{const r=(e&&e[ne]).call(e);let o;for(;(o=r.next())&&!o.done;){const s=o.value;t.call(e,s[0],s[1])}},sn=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},an=v("HTMLFormElement"),cn=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,o){return r.toUpperCase()+o}),_e=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),ln=v("RegExp"),Je=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};W(n,(o,s)=>{let i;(i=t(o,s,e))!==!1&&(r[s]=i||o)}),Object.defineProperties(e,r)},un=e=>{Je(e,(t,n)=>{if(O(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(O(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},fn=(e,t)=>{const n={},r=o=>{o.forEach(s=>{n[s]=!0})};return M(e)?r(e):r(String(e).split(t)),n},dn=()=>{},pn=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function hn(e){return!!(e&&O(e.append)&&e[Me]==="FormData"&&e[ne])}const mn=e=>{const t=new Array(10),n=(r,o)=>{if(se(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[o]=r;const s=M(r)?[]:{};return W(r,(i,c)=>{const d=n(i,o+1);!J(d)&&(s[c]=d)}),t[o]=void 0,s}}return r};return n(e,0)},gn=v("AsyncFunction"),yn=e=>e&&(se(e)||O(e))&&O(e.then)&&O(e.catch),We=((e,t)=>e?setImmediate:t?((n,r)=>(I.addEventListener("message",({source:o,data:s})=>{o===I&&s===n&&r.length&&r.shift()()},!1),o=>{r.push(o),I.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",O(I.postMessage)),wn=typeof queueMicrotask<"u"?queueMicrotask.bind(I):typeof process<"u"&&process.nextTick||We,bn=e=>e!=null&&O(e[ne]),a={isArray:M,isArrayBuffer:ze,isBuffer:Ft,isFormData:Ht,isArrayBufferView:It,isString:Ut,isNumber:$e,isBoolean:Bt,isObject:se,isPlainObject:X,isReadableStream:Jt,isRequest:Wt,isResponse:Kt,isHeaders:Gt,isUndefined:J,isDate:qt,isFile:jt,isBlob:Mt,isRegExp:ln,isFunction:O,isStream:$t,isURLSearchParams:Vt,isTypedArray:rn,isFileList:zt,forEach:W,merge:pe,extend:Qt,trim:Xt,stripBOM:Zt,inherits:Yt,toFlatObject:en,kindOf:re,kindOfTest:v,endsWith:tn,toArray:nn,forEachEntry:on,matchAll:sn,isHTMLForm:an,hasOwnProperty:_e,hasOwnProp:_e,reduceDescriptors:Je,freezeMethods:un,toObjectSet:fn,toCamelCase:cn,noop:dn,toFiniteNumber:pn,findKey:He,global:I,isContextDefined:Ve,isSpecCompliantForm:hn,toJSONObject:mn,isAsyncFn:gn,isThenable:yn,setImmediate:We,asap:wn,isIterable:bn};function g(e,t,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}a.inherits(g,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:a.toJSONObject(this.config),code:this.code,status:this.status}}});const Ke=g.prototype,Ge={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Ge[e]={value:e}});Object.defineProperties(g,Ge);Object.defineProperty(Ke,"isAxiosError",{value:!0});g.from=(e,t,n,r,o,s)=>{const i=Object.create(Ke);return a.toFlatObject(e,i,function(d){return d!==Error.prototype},c=>c!=="isAxiosError"),g.call(i,e.message,t,n,r,o),i.cause=e,i.name=e.name,s&&Object.assign(i,s),i};const Sn=null;function he(e){return a.isPlainObject(e)||a.isArray(e)}function Xe(e){return a.endsWith(e,"[]")?e.slice(0,-2):e}function ve(e,t,n){return e?e.concat(t).map(function(o,s){return o=Xe(o),!n&&s?"["+o+"]":o}).join(n?".":""):t}function En(e){return a.isArray(e)&&!e.some(he)}const Rn=a.toFlatObject(a,{},null,function(t){return/^is[A-Z]/.test(t)});function ie(e,t,n){if(!a.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=a.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(m,h){return!a.isUndefined(h[m])});const r=n.metaTokens,o=n.visitor||f,s=n.dots,i=n.indexes,d=(n.Blob||typeof Blob<"u"&&Blob)&&a.isSpecCompliantForm(t);if(!a.isFunction(o))throw new TypeError("visitor must be a function");function u(l){if(l===null)return"";if(a.isDate(l))return l.toISOString();if(a.isBoolean(l))return l.toString();if(!d&&a.isBlob(l))throw new g("Blob is not supported. Use a Buffer instead.");return a.isArrayBuffer(l)||a.isTypedArray(l)?d&&typeof Blob=="function"?new Blob([l]):Buffer.from(l):l}function f(l,m,h){let w=l;if(l&&!h&&typeof l=="object"){if(a.endsWith(m,"{}"))m=r?m:m.slice(0,-2),l=JSON.stringify(l);else if(a.isArray(l)&&En(l)||(a.isFileList(l)||a.endsWith(m,"[]"))&&(w=a.toArray(l)))return m=Xe(m),w.forEach(function(R,P){!(a.isUndefined(R)||R===null)&&t.append(i===!0?ve([m],P,s):i===null?m:m+"[]",u(R))}),!1}return he(l)?!0:(t.append(ve(h,m,s),u(l)),!1)}const p=[],y=Object.assign(Rn,{defaultVisitor:f,convertValue:u,isVisitable:he});function b(l,m){if(!a.isUndefined(l)){if(p.indexOf(l)!==-1)throw Error("Circular reference detected in "+m.join("."));p.push(l),a.forEach(l,function(w,S){(!(a.isUndefined(w)||w===null)&&o.call(t,w,a.isString(S)?S.trim():S,m,y))===!0&&b(w,m?m.concat(S):[S])}),p.pop()}}if(!a.isObject(e))throw new TypeError("data must be an object");return b(e),t}function Ce(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function Se(e,t){this._pairs=[],e&&ie(e,this,t)}const Qe=Se.prototype;Qe.append=function(t,n){this._pairs.push([t,n])};Qe.toString=function(t){const n=t?function(r){return t.call(this,r,Ce)}:Ce;return this._pairs.map(function(o){return n(o[0])+"="+n(o[1])},"").join("&")};function An(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Ze(e,t,n){if(!t)return e;const r=n&&n.encode||An;a.isFunction(n)&&(n={serialize:n});const o=n&&n.serialize;let s;if(o?s=o(t,n):s=a.isURLSearchParams(t)?t.toString():new Se(t,n).toString(r),s){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+s}return e}class xe{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){a.forEach(this.handlers,function(r){r!==null&&t(r)})}}const Ye={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Tn=typeof URLSearchParams<"u"?URLSearchParams:Se,On=typeof FormData<"u"?FormData:null,_n=typeof Blob<"u"?Blob:null,vn={isBrowser:!0,classes:{URLSearchParams:Tn,FormData:On,Blob:_n},protocols:["http","https","file","blob","url","data"]},Ee=typeof window<"u"&&typeof document<"u",me=typeof navigator=="object"&&navigator||void 0,Cn=Ee&&(!me||["ReactNative","NativeScript","NS"].indexOf(me.product)<0),xn=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Pn=Ee&&window.location.href||"http://localhost",Nn=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Ee,hasStandardBrowserEnv:Cn,hasStandardBrowserWebWorkerEnv:xn,navigator:me,origin:Pn},Symbol.toStringTag,{value:"Module"})),A={...Nn,...vn};function kn(e,t){return ie(e,new A.classes.URLSearchParams,Object.assign({visitor:function(n,r,o,s){return A.isNode&&a.isBuffer(n)?(this.append(r,n.toString("base64")),!1):s.defaultVisitor.apply(this,arguments)}},t))}function Ln(e){return a.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Dn(e){const t={},n=Object.keys(e);let r;const o=n.length;let s;for(r=0;r<o;r++)s=n[r],t[s]=e[s];return t}function et(e){function t(n,r,o,s){let i=n[s++];if(i==="__proto__")return!0;const c=Number.isFinite(+i),d=s>=n.length;return i=!i&&a.isArray(o)?o.length:i,d?(a.hasOwnProp(o,i)?o[i]=[o[i],r]:o[i]=r,!c):((!o[i]||!a.isObject(o[i]))&&(o[i]=[]),t(n,r,o[i],s)&&a.isArray(o[i])&&(o[i]=Dn(o[i])),!c)}if(a.isFormData(e)&&a.isFunction(e.entries)){const n={};return a.forEachEntry(e,(r,o)=>{t(Ln(r),o,n,0)}),n}return null}function Fn(e,t,n){if(a.isString(e))try{return(t||JSON.parse)(e),a.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const K={transitional:Ye,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",o=r.indexOf("application/json")>-1,s=a.isObject(t);if(s&&a.isHTMLForm(t)&&(t=new FormData(t)),a.isFormData(t))return o?JSON.stringify(et(t)):t;if(a.isArrayBuffer(t)||a.isBuffer(t)||a.isStream(t)||a.isFile(t)||a.isBlob(t)||a.isReadableStream(t))return t;if(a.isArrayBufferView(t))return t.buffer;if(a.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let c;if(s){if(r.indexOf("application/x-www-form-urlencoded")>-1)return kn(t,this.formSerializer).toString();if((c=a.isFileList(t))||r.indexOf("multipart/form-data")>-1){const d=this.env&&this.env.FormData;return ie(c?{"files[]":t}:t,d&&new d,this.formSerializer)}}return s||o?(n.setContentType("application/json",!1),Fn(t)):t}],transformResponse:[function(t){const n=this.transitional||K.transitional,r=n&&n.forcedJSONParsing,o=this.responseType==="json";if(a.isResponse(t)||a.isReadableStream(t))return t;if(t&&a.isString(t)&&(r&&!this.responseType||o)){const i=!(n&&n.silentJSONParsing)&&o;try{return JSON.parse(t)}catch(c){if(i)throw c.name==="SyntaxError"?g.from(c,g.ERR_BAD_RESPONSE,this,null,this.response):c}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:A.classes.FormData,Blob:A.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};a.forEach(["delete","get","head","post","put","patch"],e=>{K.headers[e]={}});const In=a.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Un=e=>{const t={};let n,r,o;return e&&e.split(`
`).forEach(function(i){o=i.indexOf(":"),n=i.substring(0,o).trim().toLowerCase(),r=i.substring(o+1).trim(),!(!n||t[n]&&In[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},Pe=Symbol("internals");function H(e){return e&&String(e).trim().toLowerCase()}function Q(e){return e===!1||e==null?e:a.isArray(e)?e.map(Q):String(e)}function Bn(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const qn=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function ue(e,t,n,r,o){if(a.isFunction(r))return r.call(this,t,n);if(o&&(t=n),!!a.isString(t)){if(a.isString(r))return t.indexOf(r)!==-1;if(a.isRegExp(r))return r.test(t)}}function jn(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function Mn(e,t){const n=a.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(o,s,i){return this[r].call(this,t,o,s,i)},configurable:!0})})}let _=class{constructor(t){t&&this.set(t)}set(t,n,r){const o=this;function s(c,d,u){const f=H(d);if(!f)throw new Error("header name must be a non-empty string");const p=a.findKey(o,f);(!p||o[p]===void 0||u===!0||u===void 0&&o[p]!==!1)&&(o[p||d]=Q(c))}const i=(c,d)=>a.forEach(c,(u,f)=>s(u,f,d));if(a.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(a.isString(t)&&(t=t.trim())&&!qn(t))i(Un(t),n);else if(a.isObject(t)&&a.isIterable(t)){let c={},d,u;for(const f of t){if(!a.isArray(f))throw TypeError("Object iterator must return a key-value pair");c[u=f[0]]=(d=c[u])?a.isArray(d)?[...d,f[1]]:[d,f[1]]:f[1]}i(c,n)}else t!=null&&s(n,t,r);return this}get(t,n){if(t=H(t),t){const r=a.findKey(this,t);if(r){const o=this[r];if(!n)return o;if(n===!0)return Bn(o);if(a.isFunction(n))return n.call(this,o,r);if(a.isRegExp(n))return n.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=H(t),t){const r=a.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||ue(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let o=!1;function s(i){if(i=H(i),i){const c=a.findKey(r,i);c&&(!n||ue(r,r[c],c,n))&&(delete r[c],o=!0)}}return a.isArray(t)?t.forEach(s):s(t),o}clear(t){const n=Object.keys(this);let r=n.length,o=!1;for(;r--;){const s=n[r];(!t||ue(this,this[s],s,t,!0))&&(delete this[s],o=!0)}return o}normalize(t){const n=this,r={};return a.forEach(this,(o,s)=>{const i=a.findKey(r,s);if(i){n[i]=Q(o),delete n[s];return}const c=t?jn(s):String(s).trim();c!==s&&delete n[s],n[c]=Q(o),r[c]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return a.forEach(this,(r,o)=>{r!=null&&r!==!1&&(n[o]=t&&a.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(o=>r.set(o)),r}static accessor(t){const r=(this[Pe]=this[Pe]={accessors:{}}).accessors,o=this.prototype;function s(i){const c=H(i);r[c]||(Mn(o,i),r[c]=!0)}return a.isArray(t)?t.forEach(s):s(t),this}};_.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);a.reduceDescriptors(_.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});a.freezeMethods(_);function fe(e,t){const n=this||K,r=t||n,o=_.from(r.headers);let s=r.data;return a.forEach(e,function(c){s=c.call(n,s,o.normalize(),t?t.status:void 0)}),o.normalize(),s}function tt(e){return!!(e&&e.__CANCEL__)}function z(e,t,n){g.call(this,e??"canceled",g.ERR_CANCELED,t,n),this.name="CanceledError"}a.inherits(z,g,{__CANCEL__:!0});function nt(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new g("Request failed with status code "+n.status,[g.ERR_BAD_REQUEST,g.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function zn(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function $n(e,t){e=e||10;const n=new Array(e),r=new Array(e);let o=0,s=0,i;return t=t!==void 0?t:1e3,function(d){const u=Date.now(),f=r[s];i||(i=u),n[o]=d,r[o]=u;let p=s,y=0;for(;p!==o;)y+=n[p++],p=p%e;if(o=(o+1)%e,o===s&&(s=(s+1)%e),u-i<t)return;const b=f&&u-f;return b?Math.round(y*1e3/b):void 0}}function Hn(e,t){let n=0,r=1e3/t,o,s;const i=(u,f=Date.now())=>{n=f,o=null,s&&(clearTimeout(s),s=null),e.apply(null,u)};return[(...u)=>{const f=Date.now(),p=f-n;p>=r?i(u,f):(o=u,s||(s=setTimeout(()=>{s=null,i(o)},r-p)))},()=>o&&i(o)]}const ee=(e,t,n=3)=>{let r=0;const o=$n(50,250);return Hn(s=>{const i=s.loaded,c=s.lengthComputable?s.total:void 0,d=i-r,u=o(d),f=i<=c;r=i;const p={loaded:i,total:c,progress:c?i/c:void 0,bytes:d,rate:u||void 0,estimated:u&&c&&f?(c-i)/u:void 0,event:s,lengthComputable:c!=null,[t?"download":"upload"]:!0};e(p)},n)},Ne=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},ke=e=>(...t)=>a.asap(()=>e(...t)),Vn=A.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,A.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(A.origin),A.navigator&&/(msie|trident)/i.test(A.navigator.userAgent)):()=>!0,Jn=A.hasStandardBrowserEnv?{write(e,t,n,r,o,s){const i=[e+"="+encodeURIComponent(t)];a.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),a.isString(r)&&i.push("path="+r),a.isString(o)&&i.push("domain="+o),s===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Wn(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Kn(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function rt(e,t,n){let r=!Wn(t);return e&&(r||n==!1)?Kn(e,t):t}const Le=e=>e instanceof _?{...e}:e;function B(e,t){t=t||{};const n={};function r(u,f,p,y){return a.isPlainObject(u)&&a.isPlainObject(f)?a.merge.call({caseless:y},u,f):a.isPlainObject(f)?a.merge({},f):a.isArray(f)?f.slice():f}function o(u,f,p,y){if(a.isUndefined(f)){if(!a.isUndefined(u))return r(void 0,u,p,y)}else return r(u,f,p,y)}function s(u,f){if(!a.isUndefined(f))return r(void 0,f)}function i(u,f){if(a.isUndefined(f)){if(!a.isUndefined(u))return r(void 0,u)}else return r(void 0,f)}function c(u,f,p){if(p in t)return r(u,f);if(p in e)return r(void 0,u)}const d={url:s,method:s,data:s,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:c,headers:(u,f,p)=>o(Le(u),Le(f),p,!0)};return a.forEach(Object.keys(Object.assign({},e,t)),function(f){const p=d[f]||o,y=p(e[f],t[f],f);a.isUndefined(y)&&p!==c||(n[f]=y)}),n}const ot=e=>{const t=B({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:o,xsrfCookieName:s,headers:i,auth:c}=t;t.headers=i=_.from(i),t.url=Ze(rt(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&i.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):"")));let d;if(a.isFormData(n)){if(A.hasStandardBrowserEnv||A.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((d=i.getContentType())!==!1){const[u,...f]=d?d.split(";").map(p=>p.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...f].join("; "))}}if(A.hasStandardBrowserEnv&&(r&&a.isFunction(r)&&(r=r(t)),r||r!==!1&&Vn(t.url))){const u=o&&s&&Jn.read(s);u&&i.set(o,u)}return t},Gn=typeof XMLHttpRequest<"u",Xn=Gn&&function(e){return new Promise(function(n,r){const o=ot(e);let s=o.data;const i=_.from(o.headers).normalize();let{responseType:c,onUploadProgress:d,onDownloadProgress:u}=o,f,p,y,b,l;function m(){b&&b(),l&&l(),o.cancelToken&&o.cancelToken.unsubscribe(f),o.signal&&o.signal.removeEventListener("abort",f)}let h=new XMLHttpRequest;h.open(o.method.toUpperCase(),o.url,!0),h.timeout=o.timeout;function w(){if(!h)return;const R=_.from("getAllResponseHeaders"in h&&h.getAllResponseHeaders()),T={data:!c||c==="text"||c==="json"?h.responseText:h.response,status:h.status,statusText:h.statusText,headers:R,config:e,request:h};nt(function(D){n(D),m()},function(D){r(D),m()},T),h=null}"onloadend"in h?h.onloadend=w:h.onreadystatechange=function(){!h||h.readyState!==4||h.status===0&&!(h.responseURL&&h.responseURL.indexOf("file:")===0)||setTimeout(w)},h.onabort=function(){h&&(r(new g("Request aborted",g.ECONNABORTED,e,h)),h=null)},h.onerror=function(){r(new g("Network Error",g.ERR_NETWORK,e,h)),h=null},h.ontimeout=function(){let P=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const T=o.transitional||Ye;o.timeoutErrorMessage&&(P=o.timeoutErrorMessage),r(new g(P,T.clarifyTimeoutError?g.ETIMEDOUT:g.ECONNABORTED,e,h)),h=null},s===void 0&&i.setContentType(null),"setRequestHeader"in h&&a.forEach(i.toJSON(),function(P,T){h.setRequestHeader(T,P)}),a.isUndefined(o.withCredentials)||(h.withCredentials=!!o.withCredentials),c&&c!=="json"&&(h.responseType=o.responseType),u&&([y,l]=ee(u,!0),h.addEventListener("progress",y)),d&&h.upload&&([p,b]=ee(d),h.upload.addEventListener("progress",p),h.upload.addEventListener("loadend",b)),(o.cancelToken||o.signal)&&(f=R=>{h&&(r(!R||R.type?new z(null,e,h):R),h.abort(),h=null)},o.cancelToken&&o.cancelToken.subscribe(f),o.signal&&(o.signal.aborted?f():o.signal.addEventListener("abort",f)));const S=zn(o.url);if(S&&A.protocols.indexOf(S)===-1){r(new g("Unsupported protocol "+S+":",g.ERR_BAD_REQUEST,e));return}h.send(s||null)})},Qn=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,o;const s=function(u){if(!o){o=!0,c();const f=u instanceof Error?u:this.reason;r.abort(f instanceof g?f:new z(f instanceof Error?f.message:f))}};let i=t&&setTimeout(()=>{i=null,s(new g(`timeout ${t} of ms exceeded`,g.ETIMEDOUT))},t);const c=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(s):u.removeEventListener("abort",s)}),e=null)};e.forEach(u=>u.addEventListener("abort",s));const{signal:d}=r;return d.unsubscribe=()=>a.asap(c),d}},Zn=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,o;for(;r<n;)o=r+t,yield e.slice(r,o),r=o},Yn=async function*(e,t){for await(const n of er(e))yield*Zn(n,t)},er=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},De=(e,t,n,r)=>{const o=Yn(e,t);let s=0,i,c=d=>{i||(i=!0,r&&r(d))};return new ReadableStream({async pull(d){try{const{done:u,value:f}=await o.next();if(u){c(),d.close();return}let p=f.byteLength;if(n){let y=s+=p;n(y)}d.enqueue(new Uint8Array(f))}catch(u){throw c(u),u}},cancel(d){return c(d),o.return()}},{highWaterMark:2})},ae=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",st=ae&&typeof ReadableStream=="function",tr=ae&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),it=(e,...t)=>{try{return!!e(...t)}catch{return!1}},nr=st&&it(()=>{let e=!1;const t=new Request(A.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Fe=64*1024,ge=st&&it(()=>a.isReadableStream(new Response("").body)),te={stream:ge&&(e=>e.body)};ae&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!te[t]&&(te[t]=a.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new g(`Response type '${t}' is not supported`,g.ERR_NOT_SUPPORT,r)})})})(new Response);const rr=async e=>{if(e==null)return 0;if(a.isBlob(e))return e.size;if(a.isSpecCompliantForm(e))return(await new Request(A.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(a.isArrayBufferView(e)||a.isArrayBuffer(e))return e.byteLength;if(a.isURLSearchParams(e)&&(e=e+""),a.isString(e))return(await tr(e)).byteLength},or=async(e,t)=>{const n=a.toFiniteNumber(e.getContentLength());return n??rr(t)},sr=ae&&(async e=>{let{url:t,method:n,data:r,signal:o,cancelToken:s,timeout:i,onDownloadProgress:c,onUploadProgress:d,responseType:u,headers:f,withCredentials:p="same-origin",fetchOptions:y}=ot(e);u=u?(u+"").toLowerCase():"text";let b=Qn([o,s&&s.toAbortSignal()],i),l;const m=b&&b.unsubscribe&&(()=>{b.unsubscribe()});let h;try{if(d&&nr&&n!=="get"&&n!=="head"&&(h=await or(f,r))!==0){let T=new Request(t,{method:"POST",body:r,duplex:"half"}),k;if(a.isFormData(r)&&(k=T.headers.get("content-type"))&&f.setContentType(k),T.body){const[D,G]=Ne(h,ee(ke(d)));r=De(T.body,Fe,D,G)}}a.isString(p)||(p=p?"include":"omit");const w="credentials"in Request.prototype;l=new Request(t,{...y,signal:b,method:n.toUpperCase(),headers:f.normalize().toJSON(),body:r,duplex:"half",credentials:w?p:void 0});let S=await fetch(l,y);const R=ge&&(u==="stream"||u==="response");if(ge&&(c||R&&m)){const T={};["status","statusText","headers"].forEach(Te=>{T[Te]=S[Te]});const k=a.toFiniteNumber(S.headers.get("content-length")),[D,G]=c&&Ne(k,ee(ke(c),!0))||[];S=new Response(De(S.body,Fe,D,()=>{G&&G(),m&&m()}),T)}u=u||"text";let P=await te[a.findKey(te,u)||"text"](S,e);return!R&&m&&m(),await new Promise((T,k)=>{nt(T,k,{data:P,headers:_.from(S.headers),status:S.status,statusText:S.statusText,config:e,request:l})})}catch(w){throw m&&m(),w&&w.name==="TypeError"&&/Load failed|fetch/i.test(w.message)?Object.assign(new g("Network Error",g.ERR_NETWORK,e,l),{cause:w.cause||w}):g.from(w,w&&w.code,e,l)}}),ye={http:Sn,xhr:Xn,fetch:sr};a.forEach(ye,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Ie=e=>`- ${e}`,ir=e=>a.isFunction(e)||e===null||e===!1,at={getAdapter:e=>{e=a.isArray(e)?e:[e];const{length:t}=e;let n,r;const o={};for(let s=0;s<t;s++){n=e[s];let i;if(r=n,!ir(n)&&(r=ye[(i=String(n)).toLowerCase()],r===void 0))throw new g(`Unknown adapter '${i}'`);if(r)break;o[i||"#"+s]=r}if(!r){const s=Object.entries(o).map(([c,d])=>`adapter ${c} `+(d===!1?"is not supported by the environment":"is not available in the build"));let i=t?s.length>1?`since :
`+s.map(Ie).join(`
`):" "+Ie(s[0]):"as no adapter specified";throw new g("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:ye};function de(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new z(null,e)}function Ue(e){return de(e),e.headers=_.from(e.headers),e.data=fe.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),at.getAdapter(e.adapter||K.adapter)(e).then(function(r){return de(e),r.data=fe.call(e,e.transformResponse,r),r.headers=_.from(r.headers),r},function(r){return tt(r)||(de(e),r&&r.response&&(r.response.data=fe.call(e,e.transformResponse,r.response),r.response.headers=_.from(r.response.headers))),Promise.reject(r)})}const ct="1.10.0",ce={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{ce[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const Be={};ce.transitional=function(t,n,r){function o(s,i){return"[Axios v"+ct+"] Transitional option '"+s+"'"+i+(r?". "+r:"")}return(s,i,c)=>{if(t===!1)throw new g(o(i," has been removed"+(n?" in "+n:"")),g.ERR_DEPRECATED);return n&&!Be[i]&&(Be[i]=!0,console.warn(o(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(s,i,c):!0}};ce.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function ar(e,t,n){if(typeof e!="object")throw new g("options must be an object",g.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let o=r.length;for(;o-- >0;){const s=r[o],i=t[s];if(i){const c=e[s],d=c===void 0||i(c,s,e);if(d!==!0)throw new g("option "+s+" must be "+d,g.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new g("Unknown option "+s,g.ERR_BAD_OPTION)}}const Z={assertOptions:ar,validators:ce},C=Z.validators;let U=class{constructor(t){this.defaults=t||{},this.interceptors={request:new xe,response:new xe}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let o={};Error.captureStackTrace?Error.captureStackTrace(o):o=new Error;const s=o.stack?o.stack.replace(/^.+\n/,""):"";try{r.stack?s&&!String(r.stack).endsWith(s.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+s):r.stack=s}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=B(this.defaults,n);const{transitional:r,paramsSerializer:o,headers:s}=n;r!==void 0&&Z.assertOptions(r,{silentJSONParsing:C.transitional(C.boolean),forcedJSONParsing:C.transitional(C.boolean),clarifyTimeoutError:C.transitional(C.boolean)},!1),o!=null&&(a.isFunction(o)?n.paramsSerializer={serialize:o}:Z.assertOptions(o,{encode:C.function,serialize:C.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),Z.assertOptions(n,{baseUrl:C.spelling("baseURL"),withXsrfToken:C.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=s&&a.merge(s.common,s[n.method]);s&&a.forEach(["delete","get","head","post","put","patch","common"],l=>{delete s[l]}),n.headers=_.concat(i,s);const c=[];let d=!0;this.interceptors.request.forEach(function(m){typeof m.runWhen=="function"&&m.runWhen(n)===!1||(d=d&&m.synchronous,c.unshift(m.fulfilled,m.rejected))});const u=[];this.interceptors.response.forEach(function(m){u.push(m.fulfilled,m.rejected)});let f,p=0,y;if(!d){const l=[Ue.bind(this),void 0];for(l.unshift.apply(l,c),l.push.apply(l,u),y=l.length,f=Promise.resolve(n);p<y;)f=f.then(l[p++],l[p++]);return f}y=c.length;let b=n;for(p=0;p<y;){const l=c[p++],m=c[p++];try{b=l(b)}catch(h){m.call(this,h);break}}try{f=Ue.call(this,b)}catch(l){return Promise.reject(l)}for(p=0,y=u.length;p<y;)f=f.then(u[p++],u[p++]);return f}getUri(t){t=B(this.defaults,t);const n=rt(t.baseURL,t.url,t.allowAbsoluteUrls);return Ze(n,t.params,t.paramsSerializer)}};a.forEach(["delete","get","head","options"],function(t){U.prototype[t]=function(n,r){return this.request(B(r||{},{method:t,url:n,data:(r||{}).data}))}});a.forEach(["post","put","patch"],function(t){function n(r){return function(s,i,c){return this.request(B(c||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:s,data:i}))}}U.prototype[t]=n(),U.prototype[t+"Form"]=n(!0)});let cr=class lt{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(s){n=s});const r=this;this.promise.then(o=>{if(!r._listeners)return;let s=r._listeners.length;for(;s-- >0;)r._listeners[s](o);r._listeners=null}),this.promise.then=o=>{let s;const i=new Promise(c=>{r.subscribe(c),s=c}).then(o);return i.cancel=function(){r.unsubscribe(s)},i},t(function(s,i,c){r.reason||(r.reason=new z(s,i,c),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new lt(function(o){t=o}),cancel:t}}};function lr(e){return function(n){return e.apply(null,n)}}function ur(e){return a.isObject(e)&&e.isAxiosError===!0}const we={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(we).forEach(([e,t])=>{we[t]=e});function ut(e){const t=new U(e),n=je(U.prototype.request,t);return a.extend(n,U.prototype,t,{allOwnKeys:!0}),a.extend(n,t,null,{allOwnKeys:!0}),n.create=function(o){return ut(B(e,o))},n}const E=ut(K);E.Axios=U;E.CanceledError=z;E.CancelToken=cr;E.isCancel=tt;E.VERSION=ct;E.toFormData=ie;E.AxiosError=g;E.Cancel=E.CanceledError;E.all=function(t){return Promise.all(t)};E.spread=lr;E.isAxiosError=ur;E.mergeConfig=B;E.AxiosHeaders=_;E.formToJSON=e=>et(a.isHTMLForm(e)?new FormData(e):e);E.getAdapter=at.getAdapter;E.HttpStatusCode=we;E.default=E;const{Axios:vr,AxiosError:Cr,CanceledError:xr,isCancel:Pr,CancelToken:Nr,VERSION:kr,all:Lr,Cancel:Dr,isAxiosError:Fr,spread:Ir,toFormData:Ur,AxiosHeaders:Br,HttpStatusCode:qr,formToJSON:jr,getAdapter:Mr,mergeConfig:zr}=E,F=E.create({baseURL:"/api/v1",timeout:1e4,headers:{"Content-Type":"application/json"}});F.interceptors.request.use(e=>{var n;const t=le();return t.token&&(e.headers.Authorization=`Bearer ${t.token}`),t.deviceFingerprint&&(e.headers["X-Device-Fingerprint"]=t.deviceFingerprint),e.headers["X-Request-ID"]=fr(),console.log(`[API Request] ${(n=e.method)==null?void 0:n.toUpperCase()} ${e.url}`,e.data),e},e=>(console.error("[API Request Error]",e),Promise.reject(e)));F.interceptors.response.use(e=>{if(console.log(`[API Response] ${e.config.url}`,JSON.stringify(e.data)),e.data){if(typeof e.data.code<"u"){console.log(`[API Response] code ${e.config.url}`,JSON.stringify(e.data));const t=e.data,n=t.code>=200&&t.code<300;if(e.data={success:n,message:t.message,data:t.data,error:t.error,timestamp:t.timestamp},!n)return Promise.reject(new Error(t.message||"请求失败"))}else if(typeof e.data.success<"u"&&!e.data.success)return Promise.reject(new Error(e.data.message||"请求失败"))}return e},e=>{console.error("[API Response Error]",e);const t=le();if(e.response){const{status:n}=e.response;n===401&&t.logout()}return Promise.reject(e)});function fr(){return`req_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}const j={get:(e,t)=>F.get(e,t).then(n=>n.data),post:(e,t,n)=>F.post(e,t,n).then(r=>r.data),put:(e,t,n)=>F.put(e,t,n).then(r=>r.data),delete:(e,t)=>F.delete(e,t).then(n=>n.data),patch:(e,t,n)=>F.patch(e,t,n).then(r=>r.data)},V={login:e=>j.post("/auth/login",e),refreshToken:e=>j.post("/auth/refresh",{refreshToken:e}),logout:()=>j.post("/auth/logout"),getCurrentUser:()=>j.get("/auth/me"),changePassword:e=>j.post("/auth/change-password",e),validateToken:()=>j.get("/auth/validate")};function ft(){var n,r,o,s;const e=navigator.userAgent,t={name:"",version:""};return e.includes("Chrome")?(t.name="Chrome",t.version=((n=e.match(/Chrome\/(\d+)/))==null?void 0:n[1])||""):e.includes("Firefox")?(t.name="Firefox",t.version=((r=e.match(/Firefox\/(\d+)/))==null?void 0:r[1])||""):e.includes("Safari")?(t.name="Safari",t.version=((o=e.match(/Version\/(\d+)/))==null?void 0:o[1])||""):e.includes("Edge")&&(t.name="Edge",t.version=((s=e.match(/Edge\/(\d+)/))==null?void 0:s[1])||""),t}function dr(){return{width:screen.width,height:screen.height,colorDepth:screen.colorDepth,pixelDepth:screen.pixelDepth}}function pr(){return{timezone:Intl.DateTimeFormat().resolvedOptions().timeZone,offset:new Date().getTimezoneOffset()}}function hr(){return{language:navigator.language,languages:navigator.languages.join(",")}}function dt(){return{platform:navigator.platform,userAgent:navigator.userAgent,cookieEnabled:navigator.cookieEnabled,doNotTrack:navigator.doNotTrack}}function mr(){try{const e=document.createElement("canvas"),t=e.getContext("2d");return t?(e.width=200,e.height=50,t.textBaseline="top",t.font="14px Arial",t.fillStyle="#f60",t.fillRect(125,1,62,20),t.fillStyle="#069",t.fillText("Go-Mail Fingerprint",2,15),t.fillStyle="rgba(102, 204, 0, 0.7)",t.fillText("Go-Mail Fingerprint",4,17),e.toDataURL()):""}catch(e){return console.warn("Canvas fingerprint generation failed:",e),""}}function gr(){try{const e=document.createElement("canvas"),t=e.getContext("webgl")||e.getContext("experimental-webgl");if(!t)return"";const n=t,r=n.getExtension("WEBGL_debug_renderer_info");return r?{vendor:n.getParameter(r.UNMASKED_VENDOR_WEBGL),renderer:n.getParameter(r.UNMASKED_RENDERER_WEBGL)}:""}catch(e){return console.warn("WebGL fingerprint generation failed:",e),""}}function yr(e){let t=0;if(e.length===0)return t.toString();for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);t=(t<<5)-t+r,t=t&t}return Math.abs(t).toString(36)}async function wr(){try{const e={browser:ft(),screen:dr(),timezone:pr(),language:hr(),platform:dt(),canvas:mr(),webgl:gr(),timestamp:Date.now()},t=JSON.stringify(e),r=`fp_${yr(t)}_${Date.now().toString(36)}`;return console.log("Generated device fingerprint:",r),r}catch(e){return console.error("Device fingerprint generation failed:",e),`fp_fallback_${Date.now().toString(36)}_${Math.random().toString(36).substr(2,9)}`}}function $r(){const e=ft(),t=dt();return{browser:`${e.name} ${e.version}`,platform:t.platform,language:navigator.language,screen:`${screen.width}x${screen.height}`}}const le=qe("auth",()=>{const e=x(""),t=x(""),n=x(null),r=x(""),o=x(!1),s=Y(()=>!!e.value&&!!n.value),i=Y(()=>{var l;return((l=n.value)==null?void 0:l.role)==="admin"}),c=async()=>{r.value||(r.value=await wr())},d=async l=>{try{o.value=!0,await c();const m=await V.login({...l,deviceFingerprint:r.value});if(m.success&&m.data)return e.value=m.data.token,t.value=m.data.refresh_token,n.value=m.data.user,localStorage.setItem("auth-token",e.value),localStorage.setItem("auth-refresh-token",t.value),localStorage.setItem("auth-user",JSON.stringify(n.value)),localStorage.setItem("device-fingerprint",r.value),{success:!0,message:"登录成功"};throw new Error(m.message||"登录失败")}catch(m){return console.error("Login error:",m),{success:!1,message:m.message||"登录失败"}}finally{o.value=!1}},u=async()=>{try{e.value&&await V.logout()}catch(l){console.error("Logout error:",l)}finally{e.value="",t.value="",n.value=null,localStorage.removeItem("auth-token"),localStorage.removeItem("auth-refresh-token"),localStorage.removeItem("auth-user"),window.location.href="/login"}},f=async()=>{try{if(!t.value)throw new Error("No refresh token available");const l=await V.refreshToken(t.value);if(l.success&&l.data)return e.value=l.data.token,t.value=l.data.refresh_token,n.value=l.data.user,localStorage.setItem("auth-token",e.value),localStorage.setItem("auth-refresh-token",t.value),localStorage.setItem("auth-user",JSON.stringify(n.value)),!0;throw new Error(l.message||"Token refresh failed")}catch(l){return console.error("Token refresh error:",l),await u(),!1}},p=async()=>{try{return e.value?(await V.validateToken()).success:!1}catch(l){return console.error("Token validation error:",l),!1}};return{token:e,refreshToken:t,user:n,deviceFingerprint:r,isLoading:o,isAuthenticated:s,isAdmin:i,login:d,logout:u,refreshAuthToken:f,validateToken:p,getCurrentUser:async()=>{try{const l=await V.getCurrentUser();return l.success&&l.data?(n.value=l.data,localStorage.setItem("auth-user",JSON.stringify(n.value)),l.data):null}catch(l){return console.error("Get current user error:",l),null}},initializeAuth:async()=>{try{const l=localStorage.getItem("auth-token"),m=localStorage.getItem("auth-refresh-token"),h=localStorage.getItem("auth-user"),w=localStorage.getItem("device-fingerprint");if(console.log("Initializing auth with saved data:",{hasToken:!!l,hasRefreshToken:!!m,hasUser:!!h,hasDeviceFingerprint:!!w}),l&&h){e.value=l,t.value=m||"",n.value=JSON.parse(h),r.value=w||"",console.log("Auth state restored, validating token...");const S=await p();if(console.log("Token validation result:",S),!S){if(console.log("Token invalid, attempting refresh..."),!await f())return console.log("Token refresh failed, logging out..."),await u(),!1;console.log("Token refreshed successfully")}return console.log("Auth initialization successful"),!0}else return console.log("No saved auth data found, initializing device fingerprint..."),await c(),!1}catch(l){return console.error("Initialize auth error:",l),await u(),!1}},initializeDeviceFingerprint:c}}),pt=qe("app",()=>{const e=x("light"),t=x("zh-CN"),n=x(!1),r=x(!1),o=x("Go-Mail管理后台"),s=Y(()=>e.value==="auto"?window.matchMedia("(prefers-color-scheme: dark)").matches:e.value==="dark");return{theme:e,language:t,sidebarCollapsed:n,loading:r,title:o,isDark:s,setTheme:l=>{e.value=l,localStorage.setItem("app-theme",l)},setLanguage:l=>{t.value=l,localStorage.setItem("app-language",l)},toggleSidebar:()=>{n.value=!n.value,localStorage.setItem("sidebar-collapsed",String(n.value))},setSidebarCollapsed:l=>{n.value=l,localStorage.setItem("sidebar-collapsed",String(l))},setLoading:l=>{r.value=l},setTitle:l=>{o.value=l,document.title=l},initializeApp:()=>{const l=localStorage.getItem("app-theme");l&&(e.value=l);const m=localStorage.getItem("app-language");m&&(t.value=m);const h=localStorage.getItem("sidebar-collapsed");h!==null&&(n.value=h==="true"),e.value==="auto"&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",()=>{e.value="auto"})},resetAppSettings:()=>{e.value="light",t.value="zh-CN",n.value=!1,localStorage.removeItem("app-theme"),localStorage.removeItem("app-language"),localStorage.removeItem("sidebar-collapsed")}}}),br=[{path:"/login",name:"Login",component:()=>N(()=>import("./Login-BLVqzNxm.js"),__vite__mapDeps([0,1,2,3,4,5])),meta:{title:"登录",requiresAuth:!1,hideInMenu:!0}},{path:"/",name:"Layout",component:()=>N(()=>import("./MainLayout-WDUUpJVe.js"),__vite__mapDeps([6,1,7,8,9,10,4,11,2,3,12])),meta:{requiresAuth:!0},redirect:"/dashboard",children:[{path:"/dashboard",name:"Dashboard",component:()=>N(()=>import("./Dashboard-DJX5NdR2.js"),__vite__mapDeps([13,14,1,15,8,3,16,10,2,17,18])),meta:{title:"系统概览",icon:"dashboard",requiresAuth:!0}},{path:"/activation-codes",name:"ActivationCodes",component:()=>N(()=>import("./ActivationCodes-D_RFjFBM.js"),__vite__mapDeps([19,2,1,16,17,20])),meta:{title:"激活管理",icon:"key",requiresAuth:!0}},{path:"/system-monitor",name:"SystemMonitor",component:()=>N(()=>import("./SystemMonitor-CcsGvvCW.js"),__vite__mapDeps([21,15,2,1,10,17,22])),meta:{title:"系统监控",icon:"monitor",requiresAuth:!0}},{path:"/system-config",name:"SystemConfig",component:()=>N(()=>import("./SystemConfig-CQ3NDQ5w.js"),__vite__mapDeps([23,15,2,1,24])),meta:{title:"系统配置",icon:"settings",requiresAuth:!0}},{path:"/mailbox-management",name:"MailboxManagement",component:()=>N(()=>import("./MailboxManagement-C4pHxqVU.js"),__vite__mapDeps([25,2,1,7,26])),meta:{title:"邮箱管理",icon:"mail",requiresAuth:!0}},{path:"/profile",name:"Profile",component:()=>N(()=>import("./Profile-BfX1Q-9j.js"),__vite__mapDeps([27,4,1,2,11,28])),meta:{title:"个人设置",icon:"person",requiresAuth:!0,hideInMenu:!0}}]},{path:"/404",name:"NotFound",component:()=>N(()=>import("./NotFound-CmmQpdft.js"),__vite__mapDeps([29,1,2,17,7,8,9,10,30])),meta:{title:"页面不存在",hideInMenu:!0}},{path:"/:pathMatch(.*)*",redirect:"/404"}],Re=ht({history:mt(),routes:br,scrollBehavior(e,t,n){return n||{top:0}}});Re.beforeEach(async(e,t,n)=>{const r=le(),o=pt();if(e.meta.title&&o.setTitle(`${e.meta.title} - Go-Mail管理后台`),e.meta.requiresAuth&&!r.isAuthenticated&&!await r.initializeAuth()){n({path:"/login",query:{redirect:e.fullPath}});return}if(e.path==="/login"&&r.isAuthenticated){n("/");return}n()});Re.afterEach((e,t)=>{console.log(`Route changed: ${t.path} -> ${e.path}`)});const Sr=gt({__name:"App",setup(e){const t=pt(),n=le(),r=Y(()=>t.isDark?At:null);return yt(async()=>{t.initializeApp(),await n.initializeAuth()}),(o,s)=>{const i=bt("router-view");return St(),wt(L(_t),{theme:r.value,locale:L(Ot),"date-locale":L(Tt)},{default:$(()=>[q(L(vt)),q(L(Ct),null,{default:$(()=>[q(L(xt),null,{default:$(()=>[q(L(Pt),null,{default:$(()=>[q(L(Nt),null,{default:$(()=>[q(i)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["theme","locale","date-locale"])}}}),Er=(e,t)=>{const n=e.__vccOpts||e;for(const[r,o]of t)n[r]=o;return n},Rr=Er(Sr,[["__scopeId","data-v-d7b4da03"]]),Ae=Et(Rr);Ae.use(Rt());Ae.use(Re);Ae.mount("#app");export{Er as _,pt as a,j as b,V as c,$r as g,F as r,le as u};

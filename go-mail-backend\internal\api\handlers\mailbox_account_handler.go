package handlers

import (
	"fmt"
	"go-mail/internal/database"
	"go-mail/internal/services"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// MailboxManagementHandler 邮箱管理处理器
type MailboxManagementHandler struct {
	service *services.MailboxManagementService
}

// NewMailboxManagementHandler 创建邮箱管理处理器
func NewMailboxManagementHandler(service *services.MailboxManagementService) *MailboxManagementHandler {
	return &MailboxManagementHandler{
		service: service,
	}
}

// BatchImportAccounts 批量导入邮箱账户
// @Summary 批量导入邮箱账户
// @Description 支持批量导入邮箱账户，格式为每行一个邮箱，使用四个短横线分隔邮箱和密码
// @Tags 邮箱管理
// @Accept json
// @Produce json
// @Param request body database.BatchImportRequest true "批量导入请求"
// @Success 200 {object} database.BatchOperationResponse
// @Failure 400 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/mailbox/batch-import [post]
func (h *MailboxManagementHandler) BatchImportAccounts(c *gin.Context) {
	var req database.BatchImportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数格式错误",
			"error":   err.Error(),
			"code":    http.StatusBadRequest,
		})
		return
	}

	// 获取用户ID（从JWT中间件获取）
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "用户未认证",
			"code":    http.StatusUnauthorized,
		})
		return
	}

	// 安全地转换用户ID类型
	var userIDStr string
	switch v := userIDInterface.(type) {
	case int:
		userIDStr = fmt.Sprintf("%d", v)
	case string:
		userIDStr = v
	default:
		userIDStr = fmt.Sprintf("%v", v)
	}

	// 执行批量导入
	result, err := h.service.BatchImportAccounts(c.Request.Context(), &req, userIDStr)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "批量导入失败",
			"error":   err.Error(),
			"code":    http.StatusInternalServerError,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "批量导入任务创建成功",
		"data":    result,
		"code":    http.StatusOK,
	})
}

// GetAccountsList 获取账户列表
// @Summary 获取账户列表
// @Description 支持筛选和分页的账户列表查询
// @Tags 邮箱管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param status query []string false "登录状态筛选"
// @Param verification_status query []string false "验证状态筛选"
// @Param import_source query []string false "导入来源筛选"
// @Param sort_by query string false "排序字段" default(created_at)
// @Param sort_order query string false "排序方向" default(desc)
// @Success 200 {object} database.PaginatedResponse[database.ExtendedAccount]
// @Failure 400 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/mailbox/accounts [get]
func (h *MailboxManagementHandler) GetAccountsList(c *gin.Context) {
	// 解析查询参数
	filter := &database.MailboxFilterRequest{
		Page:      1,
		PageSize:  20,
		SortBy:    "created_at",
		SortOrder: "desc",
	}

	// 解析分页参数
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			filter.Page = page
		}
	}

	if pageSizeStr := c.Query("page_size"); pageSizeStr != "" {
		if pageSize, err := strconv.Atoi(pageSizeStr); err == nil && pageSize > 0 && pageSize <= 100 {
			filter.PageSize = pageSize
		}
	}

	// 解析筛选参数
	if status := c.QueryArray("status"); len(status) > 0 {
		filter.Status = status
	}

	if verificationStatus := c.QueryArray("verification_status"); len(verificationStatus) > 0 {
		filter.VerificationStatus = verificationStatus
	}

	if importSource := c.QueryArray("import_source"); len(importSource) > 0 {
		filter.ImportSource = importSource
	}

	if tags := c.QueryArray("tags"); len(tags) > 0 {
		filter.Tags = tags
	}

	// 解析排序参数
	if sortBy := c.Query("sort_by"); sortBy != "" {
		filter.SortBy = sortBy
	}

	if sortOrder := c.Query("sort_order"); sortOrder != "" {
		filter.SortOrder = sortOrder
	}

	// 执行查询
	result, err := h.service.GetAccountsList(c.Request.Context(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"success": false,
			"message": "查询账户列表失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"success": true,
		"message": "查询成功",
		"data":    result,
	})
}

// StartVerificationTask 启动验证任务
// @Summary 启动邮箱验证任务
// @Description 对指定的邮箱账户启动批量验证任务
// @Tags 邮箱管理
// @Accept json
// @Produce json
// @Param request body database.VerificationTaskRequest true "验证任务请求"
// @Success 200 {object} database.BatchOperationResponse
// @Failure 400 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/mailbox/verify [post]
func (h *MailboxManagementHandler) StartVerificationTask(c *gin.Context) {
	var req database.VerificationTaskRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"success": false,
			"message": "请求参数格式错误",
			"error":   err.Error(),
		})
		return
	}

	// 获取用户ID
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    http.StatusUnauthorized,
			"success": false,
			"message": "用户未认证",
		})
		return
	}

	// 安全地转换用户ID类型
	var userIDStr string
	switch v := userIDInterface.(type) {
	case int:
		userIDStr = fmt.Sprintf("%d", v)
	case string:
		userIDStr = v
	default:
		userIDStr = fmt.Sprintf("%v", v)
	}

	// 设置默认值
	if req.ConcurrentLimit <= 0 {
		req.ConcurrentLimit = 10
	}
	if req.RetryLimit <= 0 {
		req.RetryLimit = 3
	}
	if req.VerificationType == "" {
		req.VerificationType = "login"
	}

	// 启动验证任务
	result, err := h.service.StartVerificationTask(c.Request.Context(), &req, userIDStr)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"success": false,
			"message": "启动验证任务失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"success": true,
		"message": "验证任务创建成功",
		"data":    result,
	})
}

// GetBatchOperationStatus 获取批量操作状态
// @Summary 获取批量操作状态
// @Description 查询指定批量操作的执行状态和进度
// @Tags 邮箱管理
// @Accept json
// @Produce json
// @Param operation_id path string true "操作ID"
// @Success 200 {object} database.BatchOperation
// @Failure 400 {object} gin.H
// @Failure 404 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/mailbox/batch-operation/{operation_id} [get]
func (h *MailboxManagementHandler) GetBatchOperationStatus(c *gin.Context) {
	operationID := c.Param("operation_id")
	if operationID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"success": false,
			"message": "操作ID不能为空",
		})
		return
	}

	result, err := h.service.GetBatchOperationStatus(c.Request.Context(), operationID)
	if err != nil {
		if err.Error() == "操作不存在" {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    http.StatusNotFound,
				"success": false,
				"message": "操作不存在",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"success": false,
			"message": "查询操作状态失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"success": true,
		"message": "查询成功",
		"data":    result,
	})
}

// ControlTask 控制任务
// @Summary 控制任务执行
// @Description 控制任务的启动、暂停、停止、重置等操作
// @Tags 邮箱管理
// @Accept json
// @Produce json
// @Param request body database.TaskControlRequest true "任务控制请求"
// @Success 200 {object} gin.H
// @Failure 400 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/mailbox/task-control [post]
func (h *MailboxManagementHandler) ControlTask(c *gin.Context) {
	var req database.TaskControlRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"success": false,
			"message": "请求参数格式错误",
			"error":   err.Error(),
		})
		return
	}

	// 获取用户ID
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    http.StatusUnauthorized,
			"success": false,
			"message": "用户未认证",
		})
		return
	}

	// 安全地转换用户ID类型
	var userIDStr string
	switch v := userIDInterface.(type) {
	case int:
		userIDStr = fmt.Sprintf("%d", v)
	case string:
		userIDStr = v
	default:
		userIDStr = fmt.Sprintf("%v", v)
	}

	// 执行任务控制
	err := h.service.ControlTask(c.Request.Context(), &req, userIDStr)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"success": false,
			"message": "任务控制失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"success": true,
		"message": "任务控制成功",
	})
}

// GetTaskSchedulerStatus 获取任务调度器状态
// @Summary 获取任务调度器状态
// @Description 获取所有定时任务的状态信息
// @Tags 邮箱管理
// @Accept json
// @Produce json
// @Success 200 {object} []database.TaskSchedulerStatus
// @Failure 500 {object} gin.H
// @Router /api/v1/mailbox/scheduler-status [get]
func (h *MailboxManagementHandler) GetTaskSchedulerStatus(c *gin.Context) {
	result, err := h.service.GetTaskSchedulerStatus(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"success": false,
			"message": "查询任务调度器状态失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"success": true,
		"message": "查询成功",
		"data":    result,
	})
}

// GetMailboxStatistics 获取邮箱统计信息
// @Summary 获取邮箱统计信息
// @Description 获取邮箱账户的统计信息，包括总数、状态分布等
// @Tags 邮箱管理
// @Accept json
// @Produce json
// @Success 200 {object} database.MailboxStatistics
// @Failure 500 {object} gin.H
// @Router /api/v1/mailbox/statistics [get]
func (h *MailboxManagementHandler) GetMailboxStatistics(c *gin.Context) {
	result, err := h.service.GetMailboxStatistics(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"success": false,
			"message": "查询邮箱统计信息失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"success": true,
		"message": "查询成功",
		"data":    result,
	})
}

// DeleteAccount 删除账户
// @Summary 删除单个账户
// @Description 删除指定ID的邮箱账户
// @Tags 邮箱管理
// @Accept json
// @Produce json
// @Param id path int true "账户ID"
// @Success 200 {object} gin.H
// @Failure 400 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/mailbox/accounts/{id} [delete]
func (h *MailboxManagementHandler) DeleteAccount(c *gin.Context) {
	accountID := c.Param("id")
	if accountID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"success": false,
			"message": "账户ID不能为空",
		})
		return
	}

	err := h.service.DeleteAccount(c.Request.Context(), accountID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"success": false,
			"message": "删除账户失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"success": true,
		"message": "账户删除成功",
	})
}

// ToggleAccountStatus 切换账户状态
// @Summary 启用/禁用账户
// @Description 切换指定账户的启用/禁用状态
// @Tags 邮箱管理
// @Accept json
// @Produce json
// @Param id path int true "账户ID"
// @Param request body object true "状态请求" example({"is_disabled": true})
// @Success 200 {object} gin.H
// @Failure 400 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/mailbox/accounts/{id}/status [put]
func (h *MailboxManagementHandler) ToggleAccountStatus(c *gin.Context) {
	accountID := c.Param("id")
	if accountID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"success": false,
			"message": "账户ID不能为空",
		})
		return
	}

	var req struct {
		IsDisabled bool `json:"is_disabled"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"success": false,
			"message": "请求参数格式错误",
			"error":   err.Error(),
		})
		return
	}

	err := h.service.ToggleAccountStatus(c.Request.Context(), accountID, req.IsDisabled)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"success": false,
			"message": "更新账户状态失败",
			"error":   err.Error(),
		})
		return
	}

	statusText := "启用"
	if req.IsDisabled {
		statusText = "禁用"
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"success": true,
		"message": fmt.Sprintf("账户%s成功", statusText),
	})
}

// BatchDeleteAccounts 批量删除账户
// @Summary 批量删除账户
// @Description 批量删除指定的邮箱账户
// @Tags 邮箱管理
// @Accept json
// @Produce json
// @Param request body object true "批量删除请求" example({"account_ids": [1, 2, 3]})
// @Success 200 {object} database.BatchOperationResponse
// @Failure 400 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/mailbox/batch-delete [post]
func (h *MailboxManagementHandler) BatchDeleteAccounts(c *gin.Context) {
	var req struct {
		AccountIDs []int `json:"account_ids"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"success": false,
			"message": "请求参数格式错误",
			"error":   err.Error(),
		})
		return
	}

	if len(req.AccountIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"success": false,
			"message": "账户ID列表不能为空",
		})
		return
	}

	result, err := h.service.BatchDeleteAccounts(c.Request.Context(), req.AccountIDs)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"success": false,
			"message": "批量删除账户失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"success": true,
		"message": "批量删除任务已创建",
		"data":    result,
	})
}

// BatchDisableAccounts 批量禁用账户
// @Summary 批量禁用账户
// @Description 批量禁用指定的邮箱账户
// @Tags 邮箱管理
// @Accept json
// @Produce json
// @Param request body object true "批量禁用请求" example({"account_ids": [1, 2, 3]})
// @Success 200 {object} database.BatchOperationResponse
// @Failure 400 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/mailbox/batch-disable [post]
func (h *MailboxManagementHandler) BatchDisableAccounts(c *gin.Context) {
	var req struct {
		AccountIDs []int `json:"account_ids"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"success": false,
			"message": "请求参数格式错误",
			"error":   err.Error(),
		})
		return
	}

	if len(req.AccountIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"success": false,
			"message": "账户ID列表不能为空",
		})
		return
	}

	result, err := h.service.BatchDisableAccounts(c.Request.Context(), req.AccountIDs)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"success": false,
			"message": "批量禁用账户失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"success": true,
		"message": "批量禁用任务已创建",
		"data":    result,
	})
}

// {{ AURA-X: Add - 添加代理配置API处理方法. Principle: SOLID. Approval: 寸止(ID:proxy-config). }}
// GetProxyConfig 获取代理配置
// @Summary 获取代理配置
// @Description 获取当前的全局代理配置信息
// @Tags 代理配置
// @Accept json
// @Produce json
// @Success 200 {object} database.ProxyConfigData
// @Failure 500 {object} gin.H
// @Router /api/v1/mailbox/proxy-config [get]
func (h *MailboxManagementHandler) GetProxyConfig(c *gin.Context) {
	result, err := h.service.GetProxyConfig(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"success": false,
			"message": "获取代理配置失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"success": true,
		"message": "获取代理配置成功",
		"data":    result,
	})
}

// SetProxyConfig 设置代理配置
// @Summary 设置代理配置
// @Description 设置全局代理配置信息
// @Tags 代理配置
// @Accept json
// @Produce json
// @Param request body database.ProxyConfigData true "代理配置"
// @Success 200 {object} gin.H
// @Failure 400 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/mailbox/proxy-config [post]
func (h *MailboxManagementHandler) SetProxyConfig(c *gin.Context) {
	var req database.ProxyConfigData
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"success": false,
			"message": "请求参数格式错误",
			"error":   err.Error(),
		})
		return
	}

	err := h.service.SetProxyConfig(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"success": false,
			"message": "设置代理配置失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"success": true,
		"message": "代理配置设置成功",
	})
}

// DeleteProxyConfig 删除代理配置
// @Summary 删除代理配置
// @Description 删除代理配置，重置为默认值
// @Tags 代理配置
// @Accept json
// @Produce json
// @Success 200 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/mailbox/proxy-config [delete]
func (h *MailboxManagementHandler) DeleteProxyConfig(c *gin.Context) {
	err := h.service.DeleteProxyConfig(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"success": false,
			"message": "删除代理配置失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"success": true,
		"message": "代理配置删除成功",
	})
}

// TestProxyConfig 测试代理配置
// @Summary 测试代理配置
// @Description 测试代理连通性和获取IP信息
// @Tags 代理配置
// @Accept json
// @Produce json
// @Param request body database.ProxyConfigData true "代理配置"
// @Success 200 {object} database.ProxyTestResult
// @Failure 400 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/mailbox/proxy-config/test [post]
func (h *MailboxManagementHandler) TestProxyConfig(c *gin.Context) {
	var req database.ProxyConfigData
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"success": false,
			"message": "请求参数格式错误",
			"error":   err.Error(),
		})
		return
	}

	result, err := h.service.TestProxyConfig(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"success": false,
			"message": "测试代理配置失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"success": true,
		"message": "代理测试完成",
		"data":    result,
	})
}

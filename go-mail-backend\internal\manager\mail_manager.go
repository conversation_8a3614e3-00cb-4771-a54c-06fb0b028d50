package manager

import (
	"context"
	"fmt"
	"go-mail/internal/auth"
	"go-mail/internal/database"
	"go-mail/internal/errors"
	"go-mail/internal/interfaces"
	"go-mail/internal/mail/client"
	"go-mail/internal/pool"
	"go-mail/internal/services/task_log"
	"go-mail/internal/types"
	"log/slog"
	"net/http"
	"runtime"
	"sync"
	"time"
)

// MailManager 邮件管理系统实现
type MailManager struct {
	config       *types.ManagerConfig
	accountPool  interfaces.AccountPool
	sessionPool  interfaces.SessionPool
	proxyManager interfaces.ProxyManager
	loginClient  interfaces.LoginClient
	database     *database.Database
	authService  *auth.Auth
	logger       *slog.Logger

	// 日志服务
	taskLogService   *task_log.TaskLogService
	detailLogService *task_log.DetailLogService

	// 运行状态
	running   bool
	startTime time.Time
	mutex     sync.RWMutex

	// 统计信息
	stats      *Statistics
	statsMutex sync.RWMutex

	// 生命周期管理
	ctx    context.Context
	cancel context.CancelFunc
}

// Statistics 内部统计信息
type Statistics struct {
	TotalLogins      int
	SuccessfulLogins int
	FailedLogins     int
	TotalLoginTime   time.Duration
}

// NewMailManager 创建新的邮件管理器
func NewMailManager(config *types.ManagerConfig) *MailManager {
	if config == nil {
		config = types.DefaultManagerConfig()
	}

	// 创建日志器
	logger := slog.Default()

	// 创建组件
	accountPool := pool.NewAccountPool()
	sessionPool := pool.NewSessionPool(config.SessionTimeout)
	proxyManager := pool.NewProxyManager()
	loginClient := client.NewLoginClient(config)

	// 创建认证服务（使用默认密钥）
	authService := auth.NewAuth("NUVzf4UMoPwhFATqpqnXdcYidyxiF9Uy", "K0yPL7tq8FNXmm10x4VZOR8BLxYRUOw9")

	return &MailManager{
		config:           config,
		accountPool:      accountPool,
		sessionPool:      sessionPool,
		proxyManager:     proxyManager,
		loginClient:      loginClient,
		database:         nil, // 数据库将通过SetDatabase方法设置
		authService:      authService,
		logger:           logger,
		taskLogService:   nil, // 日志服务将通过SetLogServices方法设置
		detailLogService: nil,
		stats:            &Statistics{},
	}
}

// NewMailManagerWithDatabase 创建带数据库的邮件管理器
func NewMailManagerWithDatabase(config *types.ManagerConfig, db *database.Database) *MailManager {
	manager := NewMailManager(config)
	manager.database = db
	if db != nil {
		manager.logger.Info("邮件管理器已关联数据库")
	}
	return manager
}

// NewMailManagerWithServices 创建带数据库和日志服务的邮件管理器
func NewMailManagerWithServices(config *types.ManagerConfig, db *database.Database, taskLogService *task_log.TaskLogService, detailLogService *task_log.DetailLogService) *MailManager {
	manager := NewMailManager(config)
	manager.database = db
	manager.taskLogService = taskLogService
	manager.detailLogService = detailLogService

	if db != nil {
		manager.logger.Info("邮件管理器已关联数据库")
	}
	if taskLogService != nil && detailLogService != nil {
		manager.logger.Info("邮件管理器已关联日志服务")
	}
	return manager
}

// SetDatabase 设置数据库连接
func (m *MailManager) SetDatabase(db *database.Database) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	m.database = db
	if db != nil {
		m.logger.Info("邮件管理器数据库连接已设置")
	} else {
		m.logger.Warn("邮件管理器数据库连接已清除")
	}
}

// SetLogServices 设置日志服务
func (m *MailManager) SetLogServices(taskLogService *task_log.TaskLogService, detailLogService *task_log.DetailLogService) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	m.taskLogService = taskLogService
	m.detailLogService = detailLogService
	if taskLogService != nil && detailLogService != nil {
		m.logger.Info("邮件管理器日志服务已设置")
	} else {
		m.logger.Warn("邮件管理器日志服务已清除")
	}
}

// SetAuthService 设置认证服务
func (m *MailManager) SetAuthService(authService *auth.Auth) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	m.authService = authService
	if authService != nil {
		m.logger.Info("邮件管理器认证服务已设置")
	} else {
		m.logger.Warn("邮件管理器认证服务已清除")
	}
}

// Start 启动管理器
func (m *MailManager) Start(ctx context.Context) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.running {
		return errors.NewConfigurationError(errors.ErrCodeConfigConflict, "管理器已在运行", nil)
	}

	m.logger.Info("开始启动邮件管理器...")

	// 验证必要组件
	if m.accountPool == nil {
		return errors.NewConfigurationError(errors.ErrCodeConfigInvalid, "账户池未初始化", nil)
	}
	if m.sessionPool == nil {
		return errors.NewConfigurationError(errors.ErrCodeConfigInvalid, "会话池未初始化", nil)
	}
	if m.loginClient == nil {
		return errors.NewConfigurationError(errors.ErrCodeConfigInvalid, "登录客户端未初始化", nil)
	}

	// 数据库连接是可选的，但会记录状态
	if m.database == nil {
		m.logger.Warn("邮件管理器启动时未设置数据库连接，部分功能可能受限")
	} else {
		m.logger.Debug("邮件管理器已关联数据库连接")
	}

	// 创建上下文
	m.ctx, m.cancel = context.WithCancel(ctx)
	m.running = true
	m.startTime = time.Now()

	// 启动后台任务
	go m.backgroundTasks()

	m.logger.Info("邮件管理器已启动",
		"start_time", m.startTime,
		"has_database", m.database != nil,
		"config", m.config)
	return nil
}

// Stop 停止管理器
func (m *MailManager) Stop(ctx context.Context) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if !m.running {
		return errors.NewConfigurationError(errors.ErrCodeConfigConflict, "管理器未在运行", nil)
	}

	m.logger.Info("开始停止邮件管理器...")

	// 1. 取消上下文，停止后台任务
	if m.cancel != nil {
		m.cancel()
		m.logger.Debug("后台任务已停止")
	}

	// 2. 清理会话池
	if m.sessionPool != nil {
		m.sessionPool.Cleanup()
		m.logger.Debug("会话池已清理")
	}

	// 3. 清理账户池（如果有清理方法）
	// 注意：不关闭数据库连接，因为它是共享的
	// 数据库连接由主服务器管理

	m.running = false
	m.logger.Info("邮件管理器已停止")
	return nil
}

// IsRunning 检查管理器是否在运行
func (m *MailManager) IsRunning() bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.running
}

// BatchLogin 批量登录
func (m *MailManager) BatchLogin(ctx context.Context, accounts []types.Account) (*types.BatchResult, error) {
	if !m.IsRunning() {
		return nil, errors.NewConfigurationError(errors.ErrCodeConfigInvalid, "管理器未启动", nil)
	}

	startTime := time.Now()
	result := &types.BatchResult{
		Total:   len(accounts),
		Results: make(map[string]*types.LoginResult),
		Errors:  make(map[string]error),
	}

	// 解密账户密码
	decryptedAccounts := make([]types.Account, len(accounts))
	for i, account := range accounts {
		decryptedAccount := account
		if account.Password != "" {
			// 仅对前10个账户输出详细的调试日志（安全考虑）
			if i < 10 {
				m.logger.Info("=== 账户密码解密调试 ===",
					"index", i+1,
					"email", account.Username,
					"原始密码", account.Password,
					"密码长度", len(account.Password))
			}
			
			// 检测密码格式并相应处理
			finalPassword := account.Password
			
			// 情况1: 检查是否是 AES 加密格式 (通常包含非 base64 字符或长度较长)
			if m.isLikelyAESEncrypted(account.Password) {
				if i < 10 {
					m.logger.Debug("检测到可能的AES加密密码，尝试解密",
						"email", account.Username)
				}
				
				// 尝试AES解密
				decryptedPassword, err := m.authService.DecryptPassword(account.Password)
				if err != nil {
					if i < 10 {
						m.logger.Warn("AES解密失败，可能是明文密码或bcrypt哈希", 
							"email", account.Username, 
							"error", err)
					}
					// 解密失败，保持原始密码（可能是bcrypt哈希或明文）
				} else {
					finalPassword = decryptedPassword
					if i < 10 {
						m.logger.Info("AES密码解密成功", 
							"email", account.Username,
							"解密后密码长度", len(decryptedPassword))
					}
				}
			} else {
				// 情况2: 可能是明文密码或bcrypt哈希
				if i < 10 {
					m.logger.Debug("检测到可能是明文密码或bcrypt哈希",
						"email", account.Username,
						"是否是bcrypt格式", m.isBcryptHash(account.Password))
				}
			}
			
			decryptedAccount.Password = finalPassword
			
			if i < 10 {
				m.logger.Info("=== 账户密码处理完成 ===",
					"email", account.Username,
					"最终密码长度", len(finalPassword),
					"是否使用原始密码", finalPassword == account.Password)
			}
		} else {
			// 密码为空的情况
			if i < 10 {
				m.logger.Warn("账户密码为空", 
					"index", i+1,
					"email", account.Username)
			}
		}
		decryptedAccounts[i] = decryptedAccount
	}

	// 创建工作池
	semaphore := make(chan struct{}, m.config.MaxConcurrent)
	var wg sync.WaitGroup
	var resultMutex sync.Mutex

	// 并发执行登录
	for _, account := range decryptedAccounts {
		wg.Add(1)
		go func(acc types.Account) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 执行登录
			loginResult, err := m.performSingleLogin(ctx, acc)

			// 更新结果
			resultMutex.Lock()
			if err != nil {
				result.Failed++
				result.Errors[acc.Username] = err
				m.updateStats(false, 0)
			} else {
				result.Success++
				result.Results[acc.Username] = loginResult
				m.updateStats(true, time.Since(startTime))
			}
			resultMutex.Unlock()

		}(account)
	}

	// 等待所有任务完成
	wg.Wait()
	result.Duration = time.Since(startTime)

	m.logWithSource("INFO", "批量登录完成",
		"total", result.Total,
		"success", result.Success,
		"failed", result.Failed,
		"duration", result.Duration)

	return result, nil
}

// performSingleLogin 执行单个账户登录
func (m *MailManager) performSingleLogin(ctx context.Context, account types.Account) (*types.LoginResult, error) {
	// 添加账户到池中（如果不存在）
	if err := m.accountPool.Add(account); err != nil {
		// 如果账户已存在，忽略错误
		if errors.GetErrorCode(err) != errors.ErrCodeValidationConflict {
			return nil, err
		}
	}

	// 获取代理（如果启用代理轮换）
	var proxy *types.ProxyConfig
	if m.config.ProxyRotation {
		if p, err := m.proxyManager.GetNext(); err == nil {
			proxy = p
		}
	}

	// 创建会话
	session, err := m.sessionPool.Create(account, proxy)
	if err != nil {
		return nil, err
	}

	// 创建登录日志记录器（如果日志服务可用）
	var loginLogger client.LoginLogger
	if m.taskLogService != nil && m.detailLogService != nil {
		if logger, err := task_log.NewLoginLogger(m.taskLogService, m.detailLogService, account.Username); err == nil {
			loginLogger = logger
		} else {
			m.logger.Warn("创建登录日志记录器失败", "email", account.Username, "error", err)
		}
	}

	// 执行登录
	var loginResult *types.LoginResult
	if loginLogger != nil {
		// 使用具体类型转换来调用 LoginWithLogger
		if loginClient, ok := m.loginClient.(*client.LoginClient); ok {
			loginResult, err = loginClient.LoginWithLogger(ctx, account, proxy, loginLogger)
		} else {
			loginResult, err = m.loginClient.Login(ctx, account, proxy)
		}
	} else {
		loginResult, err = m.loginClient.Login(ctx, account, proxy)
	}

	if err != nil {
		// 登录失败，移除会话
		m.sessionPool.Remove(session.ID)
		m.accountPool.UpdateLoginInfo(account.Username, false)
		return nil, err
	}

	// 登录成功，更新会话信息
	err = m.sessionPool.UpdateSession(session.ID, loginResult.JSessionID, loginResult.NavigatorSID, loginResult.RedirectURL)
	if err != nil {
		m.logger.Warn("更新会话信息失败", "session_id", session.ID, "error", err)
	}

	// 为会话设置HTTP客户端（使用登录时获得的cookies）
	if httpClient, err := m.createHTTPClientForSession(proxy, loginResult.Cookies); err == nil {
		if err := m.sessionPool.SetClient(session.ID, httpClient); err != nil {
			m.logger.Warn("设置会话HTTP客户端失败", "session_id", session.ID, "error", err)
		}
	}

	// 更新账户登录信息
	m.accountPool.UpdateLoginInfo(account.Username, true)

	// 保存账户信息到数据库
	if m.database != nil {
		err = m.database.SaveAccount(
			account.Username,
			account.Password,
			loginResult.Cookies,
			session.ID,
			loginResult.JSessionID,
			loginResult.NavigatorSID,
		)
		if err != nil {
			m.logger.Warn("保存账户信息到数据库失败", "email", account.Username, "error", err)
		} else {
			m.logger.Info("账户信息已保存到数据库", "email", account.Username)
		}
	}

	// 设置会话ID
	loginResult.SessionID = session.ID

	return loginResult, nil
}

// BatchLogout 批量登出
func (m *MailManager) BatchLogout(ctx context.Context, sessionIDs []string) error {
	if !m.IsRunning() {
		return errors.NewConfigurationError(errors.ErrCodeConfigInvalid, "管理器未启动", nil)
	}

	var wg sync.WaitGroup
	semaphore := make(chan struct{}, m.config.MaxConcurrent)

	for _, sessionID := range sessionIDs {
		wg.Add(1)
		go func(id string) {
			defer wg.Done()

			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 获取会话
			session, err := m.sessionPool.Get(id)
			if err != nil {
				m.logger.Warn("获取会话失败", "session_id", id, "error", err)
				return
			}

			// 执行登出
			if err := m.loginClient.Logout(ctx, session); err != nil {
				m.logger.Warn("登出失败", "session_id", id, "error", err)
			}

			// 移除会话
			m.sessionPool.Remove(id)
		}(sessionID)
	}

	wg.Wait()
	return nil
}

// BatchHealthCheck 批量健康检查
func (m *MailManager) BatchHealthCheck(ctx context.Context) (*types.HealthReport, error) {
	if !m.IsRunning() {
		return nil, errors.NewConfigurationError(errors.ErrCodeConfigInvalid, "管理器未启动", nil)
	}

	report := &types.HealthReport{
		Timestamp:     time.Now(),
		OverallStatus: "healthy",
		AccountStatus: make(map[string]types.AccountStatus),
		SessionStatus: make(map[string]types.SessionStatus),
		ProxyStatus:   make(map[string]bool),
	}

	// 检查账户状态
	accounts := m.accountPool.GetAccountInfo()
	for _, account := range accounts {
		report.AccountStatus[account.Username] = account.Status
	}

	// 检查会话状态
	sessions := m.sessionPool.GetActive()
	for _, session := range sessions {
		report.SessionStatus[session.ID] = session.Status
	}

	// 检查代理状态
	proxyStatuses := m.proxyManager.GetProxyStatus()
	for _, status := range proxyStatuses {
		report.ProxyStatus[status.ID] = status.Enabled
	}

	// 清理过期会话
	m.sessionPool.Cleanup()

	return report, nil
}

// updateStats 更新统计信息
func (m *MailManager) updateStats(success bool, duration time.Duration) {
	m.statsMutex.Lock()
	defer m.statsMutex.Unlock()

	m.stats.TotalLogins++
	if success {
		m.stats.SuccessfulLogins++
		m.stats.TotalLoginTime += duration
	} else {
		m.stats.FailedLogins++
	}
}

// backgroundTasks 后台任务
func (m *MailManager) backgroundTasks() {
	ticker := time.NewTicker(m.config.HealthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			// 执行定期清理和健康检查
			m.sessionPool.Cleanup()
			m.proxyManager.BatchHealthCheck()
		}
	}
}

// 实现剩余的接口方法

// AddAccount 添加账户
func (m *MailManager) AddAccount(account types.Account) error {
	return m.accountPool.Add(account)
}

// RemoveAccount 移除账户
func (m *MailManager) RemoveAccount(username string) error {
	// 先移除相关会话
	if session, err := m.sessionPool.GetByAccount(username); err == nil {
		m.sessionPool.Remove(session.ID)
	}
	return m.accountPool.Remove(username)
}

// GetAccountStatus 获取账户状态
func (m *MailManager) GetAccountStatus(username string) (*types.AccountStatus, error) {
	account, err := m.accountPool.Get(username)
	if err != nil {
		return nil, err
	}
	return &account.Status, nil
}

// ListAccounts 列出所有账户
func (m *MailManager) ListAccounts() []types.AccountInfo {
	return m.accountPool.GetAccountInfo()
}

// SetProxyPool 设置代理池
func (m *MailManager) SetProxyPool(proxies []types.ProxyConfig) error {
	// 清空现有代理
	m.proxyManager.Clear()

	// 添加新代理
	for _, proxy := range proxies {
		if err := m.proxyManager.Add(proxy); err != nil {
			return err
		}
	}
	return nil
}

// RotateProxy 轮换代理
func (m *MailManager) RotateProxy(sessionID string) error {
	// 获取新代理
	proxy, err := m.proxyManager.GetNext()
	if err != nil {
		return err
	}

	// 这里应该更新会话的代理配置
	// 简化实现：记录日志
	m.logger.Info("代理轮换", "session_id", sessionID, "new_proxy", proxy.ID)
	return nil
}

// GetProxyStatus 获取代理状态
func (m *MailManager) GetProxyStatus() []types.ProxyStatus {
	return m.proxyManager.GetProxyStatus()
}

// UpdateConfig 更新配置
func (m *MailManager) UpdateConfig(config *types.ManagerConfig) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	m.config = config
	m.logger.Info("配置已更新", "config", config)
	return nil
}

// GetConfig 获取配置
func (m *MailManager) GetConfig() *types.ManagerConfig {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// 返回副本
	configCopy := *m.config
	return &configCopy
}

// logWithSource 带源码位置的日志辅助函数
func (m *MailManager) logWithSource(level string, msg string, args ...interface{}) {
	_, file, line, ok := runtime.Caller(1)
	if ok {
		// 提取文件名（去掉路径）
		for i := len(file) - 1; i >= 0; i-- {
			if file[i] == '/' || file[i] == '\\' {
				file = file[i+1:]
				break
			}
		}
		// 添加源码位置信息
		args = append([]interface{}{"source", file + ":" + fmt.Sprintf("%d", line)}, args...)
	}

	switch level {
	case "DEBUG":
		m.logger.Debug(msg, args...)
	case "INFO":
		m.logger.Info(msg, args...)
	case "WARN":
		m.logger.Warn(msg, args...)
	case "ERROR":
		m.logger.Error(msg, args...)
	default:
		m.logger.Info(msg, args...)
	}
}

// GetSessionByAccount 根据账户名获取会话
func (m *MailManager) GetSessionByAccount(username string) (*types.Session, error) {
	return m.sessionPool.GetByAccount(username)
}

// {{ AURA-X: Modify - 修改方法签名以支持cookies参数. Approval: 寸止(ID:**********). }}
// createHTTPClientForSession 为会话创建HTTP客户端
func (m *MailManager) createHTTPClientForSession(proxyConfig *types.ProxyConfig, cookies []*http.Cookie) (*http.Client, error) {
	// 使用LoginClient的CreateHTTPClientWithCookies方法
	loginClient := m.loginClient.(*client.LoginClient)
	return loginClient.CreateHTTPClientWithCookies(proxyConfig, cookies)
}

// GetStatistics 获取统计信息
func (m *MailManager) GetStatistics() *types.Statistics {
	m.statsMutex.RLock()
	defer m.statsMutex.RUnlock()

	avgLoginTime := time.Duration(0)
	if m.stats.SuccessfulLogins > 0 {
		avgLoginTime = m.stats.TotalLoginTime / time.Duration(m.stats.SuccessfulLogins)
	}

	uptime := time.Duration(0)
	if !m.startTime.IsZero() {
		uptime = time.Since(m.startTime)
	}

	return &types.Statistics{
		TotalAccounts:    m.accountPool.Count(),
		ActiveSessions:   len(m.sessionPool.GetActive()),
		TotalLogins:      m.stats.TotalLogins,
		SuccessfulLogins: m.stats.SuccessfulLogins,
		FailedLogins:     m.stats.FailedLogins,
		AverageLoginTime: avgLoginTime,
		Uptime:           uptime,
	}
}

// GetLogs 获取日志（简化实现）
func (m *MailManager) GetLogs(filter types.LogFilter) []types.LogEntry {
	// 这里应该实现真正的日志查询
	// 简化实现：返回空列表
	return []types.LogEntry{}
}

// isLikelyAESEncrypted 检测密码是否可能是AES加密格式
func (m *MailManager) isLikelyAESEncrypted(password string) bool {
	// AES加密的特征：
	// 1. 通常比bcrypt哈希长（AES加密后包含nonce和tag）
	// 2. 是有效的base64编码
	// 3. 不以$2a$开头（bcrypt的前缀）
	
	if len(password) < 10 {
		return false
	}
	
	// 如果是bcrypt格式，直接返回false
	if m.isBcryptHash(password) {
		return false
	}
	
	// 检查是否是有效的base64编码
	_, err := m.authService.DecryptPassword(password)
	if err == nil {
		// 如果能成功解密，很可能是AES加密
		return true
	}
	
	// 或者检查是否包含非标准base64字符（但这是简化的检测）
	// 实际的AES加密数据总是base64编码的
	return false
}

// isBcryptHash 检测密码是否是bcrypt哈希格式
func (m *MailManager) isBcryptHash(password string) bool {
	// bcrypt哈希的特征：以$2a$, $2b$, $2x$, $2y$ 开头
	if len(password) < 4 {
		return false
	}
	
	prefix := password[:4]
	return prefix == "$2a$" || prefix == "$2b$" || 
		   prefix == "$2x$" || prefix == "$2y$"
}

<template>
  <n-card title="任务日志" class="task-log-panel">
    <template #header-extra>
      <n-space>
        <n-button @click="refreshLogs" size="small">
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新
        </n-button>
      </n-space>
    </template>

    <!-- 筛选器 -->
    <div class="filter-section mb-4">
      <n-space>
        <n-date-picker
          v-model:value="filters.dateRange"
          type="daterange"
          placeholder="选择时间范围"
          :default-value="[Date.now() - 3 * 24 * 60 * 60 * 1000, Date.now()]"
          style="width: 240px"
        />
        <n-select
          v-model:value="filters.operationType"
          :options="operationTypeOptions"
          placeholder="操作类型"
          clearable
          style="width: 120px"
        />
        <n-select
          v-model:value="filters.status"
          :options="statusOptions"
          placeholder="状态"
          clearable
          style="width: 120px"
        />
        <n-input
          v-model:value="filters.email"
          placeholder="邮箱地址"
          clearable
          style="width: 200px"
        />
        <n-button @click="applyFilters" type="primary" size="small">筛选</n-button>
        <n-button @click="resetFilters" size="small">重置</n-button>
      </n-space>
    </div>

    <!-- 日志表格 -->
    <n-data-table
      :columns="columns"
      :data="logs"
      :loading="loading"
      :pagination="pagination"
      :row-key="(row: TaskLog) => row.id"
      @update:page="handlePageChange"
      @update:page-size="handlePageSizeChange"
    />

    <!-- 详细日志模态窗口 -->
    <n-modal
      v-model:show="showDetailModal"
      preset="card"
      title="详细日志"
      style="width: 80%; max-width: 1200px"
      :mask-closable="false"
    >
      <template #header-extra>
        <n-button @click="showDetailModal = false" quaternary circle>
          <template #icon>
            <n-icon><CloseOutline /></n-icon>
          </template>
        </n-button>
      </template>

      <n-spin :show="detailLoading">
        <div v-if="currentDetailLog && currentDetailLog.taskLog" class="detail-log-container">
          <!-- 基本信息 -->
          <div class="log-header mb-4">
            <n-space>
              <n-tag :type="getStatusType(currentDetailLog.taskLog.status)">
                {{ getStatusText(currentDetailLog.taskLog.status) }}
              </n-tag>
              <span class="text-gray-600">{{ currentDetailLog.taskLog.email }}</span>
              <span class="text-gray-500">{{
                formatTime(currentDetailLog.taskLog.start_time)
              }}</span>
            </n-space>
          </div>

          <!-- 执行步骤时间线 -->
          <n-timeline
            v-if="
              currentDetailLog.detail &&
              currentDetailLog.detail.steps &&
              currentDetailLog.detail.steps.length > 0
            "
          >
            <n-timeline-item
              v-for="(step, index) in currentDetailLog.detail.steps"
              :key="index"
              :type="getStepType(step.status)"
              :title="step.step"
              :time="formatTime(step.timestamp)"
            >
              <div class="step-content">
                <p class="mb-2">{{ step.details }}</p>
                <div v-if="step.duration" class="text-sm text-gray-500 mb-2">
                  耗时: {{ step.duration }}ms
                </div>
                <div v-if="step.error_message" class="text-red-500 mb-2">
                  错误: {{ step.error_message }}
                </div>
                <div
                  v-if="step.response_data && Object.keys(step.response_data).length > 0"
                  class="response-data"
                >
                  <n-code
                    :code="JSON.stringify(step.response_data, null, 2)"
                    language="json"
                    show-line-numbers
                  />
                </div>
              </div>
            </n-timeline-item>
          </n-timeline>

          <!-- 如果没有详细步骤，显示基本信息 -->
          <div v-else class="no-steps">
            <p class="text-gray-500">暂无详细执行步骤记录</p>
            <div
              v-if="currentDetailLog.taskLog && currentDetailLog.taskLog.error_message"
              class="mt-4"
            >
              <h4 class="mb-2">错误信息:</h4>
              <n-code :code="currentDetailLog.taskLog.error_message" language="text" />
            </div>
          </div>
        </div>
      </n-spin>
    </n-modal>
  </n-card>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, h } from 'vue'
import { taskLogApi } from '@/api/task-log'
import type {
  TaskLog as ApiTaskLog,
  TaskLogDetailResponse,
  TaskLogFilterRequest,
} from '@/api/task-log'
import {
  NCard,
  NButton,
  NSpace,
  NIcon,
  NDatePicker,
  NSelect,
  NInput,
  NDataTable,
  NTag,
  NModal,
  NTimeline,
  NTimelineItem,
  NCode,
  NSpin,
  useMessage,
} from 'naive-ui'
import {
  RefreshOutline,
  CheckmarkCircleOutline,
  CloseCircleOutline,
  TimeOutline,
  InformationCircleOutline,
  CloudUploadOutline,
  CheckmarkOutline,
  CloseOutline,
} from '@vicons/ionicons5'

const message = useMessage()

// 使用从API文件导入的类型定义
type TaskLog = ApiTaskLog

// 响应式数据
const loading = ref(false)
const logs = ref<TaskLog[]>([])

// 详细日志模态窗口相关
const showDetailModal = ref(false)
const detailLoading = ref(false)
const currentDetailLog = ref<TaskLogDetailResponse | null>(null)

// 筛选器
const filters = reactive({
  dateRange: [Date.now() - 3 * 24 * 60 * 60 * 1000, Date.now()] as [number, number],
  operationType: null as string | null,
  status: null as string | null,
  email: '',
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
})

// 选项数据
const operationTypeOptions = [
  { label: '批量导入', value: 'import' },
  { label: '邮箱验证', value: 'verify' },
]

const statusOptions = [
  { label: '成功', value: 'success' },
  { label: '失败', value: 'failed' },
  { label: '进行中', value: 'running' },
  { label: '等待中', value: 'pending' },
]

// 状态格式化
const formatStatus = (status: string) => {
  const statusMap = {
    success: { type: 'success' as const, text: '成功', icon: CheckmarkCircleOutline },
    failed: { type: 'error' as const, text: '失败', icon: CloseCircleOutline },
    running: { type: 'info' as const, text: '进行中', icon: TimeOutline },
    pending: { type: 'warning' as const, text: '等待中', icon: TimeOutline },
  }
  return (
    statusMap[status as keyof typeof statusMap] || {
      type: 'default' as const,
      text: status,
      icon: InformationCircleOutline,
    }
  )
}

// 操作类型格式化
const formatOperationType = (type: string) => {
  const typeMap = {
    import: { text: '批量导入', icon: CloudUploadOutline, color: '#2080f0' },
    verify: { text: '邮箱验证', icon: CheckmarkOutline, color: '#18a058' },
  }
  return (
    typeMap[type as keyof typeof typeMap] || {
      text: type,
      icon: InformationCircleOutline,
      color: '#666',
    }
  )
}

// 表格列定义
const columns = computed(() => [
  {
    title: '时间',
    key: 'start_time',
    width: 160,
    render: (row: TaskLog) => {
      return new Date(row.start_time).toLocaleString()
    },
  },
  {
    title: '操作类型',
    key: 'operation_type',
    width: 120,
    render: (row: TaskLog) => {
      const type = formatOperationType(row.operation_type)
      return h(
        NSpace,
        { align: 'center' },
        {
          default: () => [
            h(NIcon, { color: type.color }, { default: () => h(type.icon) }),
            h('span', type.text),
          ],
        }
      )
    },
  },
  {
    title: '邮箱地址',
    key: 'email',
    width: 200,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row: TaskLog) => {
      const status = formatStatus(row.status)
      return h(
        NSpace,
        { align: 'center' },
        {
          default: () => [
            h(NIcon, {}, { default: () => h(status.icon) }),
            h(NTag, { type: status.type }, { default: () => status.text }),
          ],
        }
      )
    },
  },
  {
    title: '耗时',
    key: 'duration_ms',
    width: 100,
    render: (row: TaskLog) => {
      if (!row.duration_ms) return '-'
      if (row.duration_ms < 1000) return `${row.duration_ms}ms`
      return `${(row.duration_ms / 1000).toFixed(1)}s`
    },
  },
  {
    title: '详细日志',
    key: 'detail_log',
    width: 200,
    render: (row: TaskLog) => {
      return h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          text: true,
          onClick: () => showDetailLog(row),
        },
        { default: () => '查看详细日志' }
      )
    },
  },
  {
    title: '批次ID',
    key: 'batch_id',
    width: 120,
    render: (row: TaskLog) => {
      return row.batch_id || '-'
    },
  },
])

// 加载日志数据
const loadLogs = async () => {
  try {
    loading.value = true

    // 构建查询参数
    const params: TaskLogFilterRequest = {
      page: pagination.page,
      page_size: pagination.pageSize,
    }

    // 添加筛选条件
    if (filters.operationType) {
      params.operation_type = filters.operationType
    }
    if (filters.status) {
      params.status = filters.status
    }
    if (filters.email) {
      params.email = filters.email
    }
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.start_time = new Date(filters.dateRange[0]).toISOString()
      params.end_time = new Date(filters.dateRange[1]).toISOString()
    }

    // 调用API
    const response = await taskLogApi.getTaskLogs(params)

    // 处理响应数据
    const apiResponse = (response as any).data || response
    console.log(apiResponse)
    if ((apiResponse.code === 200 || apiResponse.success) && apiResponse.data) {
      // 任务日志API返回的是小写字段名
      logs.value = apiResponse.data.items || []
      pagination.itemCount = apiResponse.data.total || 0
    } else {
      logs.value = []
      pagination.itemCount = 0
      if (apiResponse.message) {
        message.error('加载任务日志失败：' + apiResponse.message)
      }
    }
  } catch (error) {
    message.error('加载任务日志失败')
    console.error(error)
    // 确保在错误情况下也设置默认值
    logs.value = []
    pagination.itemCount = 0
  } finally {
    loading.value = false
  }
}

const refreshLogs = () => {
  loadLogs()
}

const applyFilters = () => {
  pagination.page = 1
  loadLogs()
}

const resetFilters = () => {
  filters.dateRange = [Date.now() - 3 * 24 * 60 * 60 * 1000, Date.now()]
  filters.operationType = null
  filters.status = null
  filters.email = ''
  pagination.page = 1
  loadLogs()
}

const handlePageChange = (page: number) => {
  pagination.page = page
  loadLogs()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  loadLogs()
}

// 详细日志相关函数
const showDetailLog = async (row: TaskLog) => {
  showDetailModal.value = true
  detailLoading.value = true

  try {
    // 调用API获取详细日志
    const response = await taskLogApi.getTaskLogDetail(row.id)

    // 处理响应数据
    const apiResponse = (response as any).data || response
    if ((apiResponse.code === 200 || apiResponse.success) && apiResponse.data) {
      // 修复：将API返回的 task_log 字段映射为 taskLog（驼峰命名）
      const responseData = apiResponse.data

      // 验证数据完整性
      if (responseData.task_log && responseData.detail) {
        currentDetailLog.value = {
          taskLog: responseData.task_log, // 将 task_log 映射为 taskLog
          detail: responseData.detail,
        }
      } else {
        message.error('详细日志数据格式不正确')
        currentDetailLog.value = null
      }
    } else {
      message.error('获取详细日志失败：' + (apiResponse.message || '未知错误'))
      currentDetailLog.value = null
    }
  } catch (error) {
    message.error('获取详细日志失败')
    console.error(error)
    currentDetailLog.value = null
  } finally {
    detailLoading.value = false
  }
}

// 格式化时间
const formatTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleString()
}

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap = {
    success: 'success' as const,
    failed: 'error' as const,
    running: 'info' as const,
    pending: 'warning' as const,
  }
  return statusMap[status as keyof typeof statusMap] || ('default' as const)
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    success: '成功',
    failed: '失败',
    running: '进行中',
    pending: '等待中',
  }
  return statusMap[status as keyof typeof statusMap] || status
}

// 获取步骤类型
const getStepType = (status: string) => {
  const typeMap = {
    success: 'success' as const,
    failed: 'error' as const,
    running: 'info' as const,
  }
  return typeMap[status as keyof typeof typeMap] || ('default' as const)
}

// 生命周期
onMounted(() => {
  loadLogs()
})
</script>

<style scoped>
.task-log-panel {
  margin-top: 16px;
}

.filter-section {
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.error-text {
  color: #d03050;
  cursor: pointer;
}

/* 详细日志模态窗口样式 */
.detail-log-container {
  max-height: 70vh;
  overflow-y: auto;
}

.log-header {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 16px;
}

.step-content {
  padding: 8px 0;
}

.step-content p {
  margin: 0 0 8px 0;
  color: #333;
}

.response-data {
  margin-top: 8px;
  padding: 8px;
  background: #f5f5f5;
  border-radius: 4px;
  border-left: 3px solid #007bff;
}

.no-steps {
  text-align: center;
  padding: 40px 20px;
}

.no-steps h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-weight: 500;
}

/* 工具类 */
.mb-2 {
  margin-bottom: 8px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-600 {
  color: #4b5563;
}

.text-red-500 {
  color: #ef4444;
}

.text-sm {
  font-size: 0.875rem;
}
</style>

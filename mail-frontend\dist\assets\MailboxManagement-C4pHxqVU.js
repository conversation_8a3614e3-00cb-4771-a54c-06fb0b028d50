import{r as J,_ as re}from"./index-hmWk46K2.js";import{u as ie,H as ge,R as ye,S as tt,Q as we,j as W,k as ce,T as st,l as xe,i as $e,B as O,J as H,I as de,U as ot,V as lt,O as nt,w as le,v as ne,h as F,F as se,K as he,W as at,X as fe,Y as rt,L as it,Z as ut,_ as Ae,$ as X,a0 as Y,a1 as ct,a2 as dt,a3 as pt,a4 as vt,a5 as ft,a6 as ze,a7 as Ie,a8 as _t}from"./ui-DUh7fRR5.js";import{k as V,W as _,Q as d,X as o,a1 as Re,r as I,e as oe,c as Z,w as ae,O as K,R as s,K as e,S as t,a4 as A,j as w,$ as P,F as Ce,a2 as Ve,H as Ee,m as L,o as Se,E as gt}from"./vendor-RHijBMdK.js";import{D as mt}from"./SpeedometerOutline-D-Vl8HvQ.js";const kt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},yt=o("circle",{cx:"256",cy:"256",r:"208",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"},null,-1),ht=o("path",{fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32",d:"M108.92 108.92l294.16 294.16"},null,-1),wt=[yt,ht],xt=V({name:"BanOutline",render:function(p,g){return d(),_("svg",kt,wt)}}),bt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},$t=o("path",{d:"M448 256c0-106-86-192-192-192S64 150 64 256s86 192 192 192s192-86 192-192z",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"},null,-1),Ct=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M352 176L217.6 336L160 272"},null,-1),St=[$t,Ct],Me=V({name:"CheckmarkCircleOutline",render:function(p,g){return d(),_("svg",bt,St)}}),Mt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Tt=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M416 128L192 384l-96-96"},null,-1),Pt=[Tt],Ot=V({name:"CheckmarkOutline",render:function(p,g){return d(),_("svg",Mt,Pt)}}),Lt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},zt=o("path",{d:"M448 256c0-106-86-192-192-192S64 150 64 256s86 192 192 192s192-86 192-192z",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"},null,-1),It=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M320 320L192 192"},null,-1),Dt=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M192 320l128-128"},null,-1),jt=[zt,It,Dt],Te=V({name:"CloseCircleOutline",render:function(p,g){return d(),_("svg",Lt,jt)}}),Nt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Bt=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M368 368L144 144"},null,-1),Ut=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M368 144L144 368"},null,-1),At=[Bt,Ut],Rt=V({name:"CloseOutline",render:function(p,g){return d(),_("svg",Nt,At)}}),Vt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Et=o("path",{d:"M320 367.79h76c55 0 100-29.21 100-83.6s-53-81.47-96-83.6c-8.89-85.06-71-136.8-144-136.8c-69 0-113.44 45.79-128 91.2c-60 5.7-112 43.88-112 106.4s54 106.4 120 106.4h56",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),Ft=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M320 255.79l-64-64l-64 64"},null,-1),qt=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M256 448.21V207.79"},null,-1),Jt=[Et,Ft,qt],Fe=V({name:"CloudUploadOutline",render:function(p,g){return d(),_("svg",Vt,Jt)}}),Ht={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Wt=o("circle",{cx:"256",cy:"256",r:"192",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),Gt=[Wt],Kt=V({name:"EllipseOutline",render:function(p,g){return d(),_("svg",Ht,Gt)}}),Qt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Xt=Re('<path d="M256 48C141.13 48 48 141.13 48 256s93.13 208 208 208s208-93.13 208-208S370.87 48 256 48z" fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="32"></path><path d="M256 48c-58.07 0-112.67 93.13-112.67 208S197.93 464 256 464s112.67-93.13 112.67-208S314.07 48 256 48z" fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="32"></path><path d="M117.33 117.33c38.24 27.15 86.38 43.34 138.67 43.34s100.43-16.19 138.67-43.34" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32"></path><path d="M394.67 394.67c-38.24-27.15-86.38-43.34-138.67-43.34s-100.43 16.19-138.67 43.34" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32"></path><path fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="32" d="M256 48v416"></path><path fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="32" d="M464 256H48"></path>',6),Yt=[Xt],Zt=V({name:"GlobeOutline",render:function(p,g){return d(),_("svg",Qt,Yt)}}),es={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},ts=o("path",{d:"M248 64C146.39 64 64 146.39 64 248s82.39 184 184 184s184-82.39 184-184S349.61 64 248 64z",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"},null,-1),ss=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M220 220h32v116"},null,-1),os=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-miterlimit":"10","stroke-width":"32",d:"M208 340h88"},null,-1),ls=o("path",{d:"M248 130a26 26 0 1 0 26 26a26 26 0 0 0-26-26z",fill:"currentColor"},null,-1),ns=[ts,ss,os,ls],be=V({name:"InformationCircleOutline",render:function(p,g){return d(),_("svg",es,ns)}}),as={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},rs=o("path",{d:"M256 48c-79.5 0-144 61.39-144 137c0 87 96 224.87 131.25 272.49a15.77 15.77 0 0 0 25.5 0C304 409.89 400 272.07 400 185c0-75.61-64.5-137-144-137z",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),is=o("circle",{cx:"256",cy:"192",r:"48",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),us=[rs,is],cs=V({name:"LocationOutline",render:function(p,g){return d(),_("svg",as,us)}}),ds={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},ps=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M176 96h16v320h-16z"},null,-1),vs=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M320 96h16v320h-16z"},null,-1),fs=[ps,vs],_s=V({name:"PauseOutline",render:function(p,g){return d(),_("svg",ds,fs)}}),gs={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},ms=o("path",{d:"M112 111v290c0 17.44 17 28.52 31 20.16l247.9-148.37c12.12-7.25 12.12-26.33 0-33.58L143 90.84c-14-8.36-31 2.72-31 20.16z",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"},null,-1),ks=[ms],De=V({name:"PlayOutline",render:function(p,g){return d(),_("svg",gs,ks)}}),ys={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},hs=o("path",{d:"M320 146s24.36-12-64-12a160 160 0 1 0 160 160",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-miterlimit":"10","stroke-width":"32"},null,-1),ws=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M256 58l80 80l-80 80"},null,-1),xs=[hs,ws],_e=V({name:"RefreshOutline",render:function(p,g){return d(),_("svg",ys,xs)}}),bs={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},$s=o("ellipse",{cx:"256",cy:"128",rx:"192",ry:"80",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-miterlimit":"10","stroke-width":"32"},null,-1),Cs=o("path",{d:"M448 214c0 44.18-86 80-192 80S64 258.18 64 214",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-miterlimit":"10","stroke-width":"32"},null,-1),Ss=o("path",{d:"M448 300c0 44.18-86 80-192 80S64 344.18 64 300",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-miterlimit":"10","stroke-width":"32"},null,-1),Ms=o("path",{d:"M64 127.24v257.52C64 428.52 150 464 256 464s192-35.48 192-79.24V127.24",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-miterlimit":"10","stroke-width":"32"},null,-1),Ts=[$s,Cs,Ss,Ms],je=V({name:"ServerOutline",render:function(p,g){return d(),_("svg",bs,Ts)}}),Ps={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Os=o("rect",{x:"96",y:"96",width:"320",height:"320",rx:"24",ry:"24",fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"32"},null,-1),Ls=[Os],Ne=V({name:"StopOutline",render:function(p,g){return d(),_("svg",Ps,Ls)}}),zs={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Is=o("path",{d:"M434.67 285.59v-29.8c0-98.73-80.24-178.79-179.2-178.79a179 179 0 0 0-140.14 67.36m-38.53 82v29.8C76.8 355 157 435 256 435a180.45 180.45 0 0 0 140-66.92",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),Ds=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M32 256l44-44l46 44"},null,-1),js=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M480 256l-44 44l-46-44"},null,-1),Ns=[Is,Ds,js],Bs=V({name:"SyncOutline",render:function(p,g){return d(),_("svg",zs,Ns)}}),Us={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},As=o("path",{d:"M256 64C150 64 64 150 64 256s86 192 192 192s192-86 192-192S362 64 256 64z",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"},null,-1),Rs=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M256 128v144h96"},null,-1),Vs=[As,Rs],Be=V({name:"TimeOutline",render:function(p,g){return d(),_("svg",Us,Vs)}}),Es={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Fs=Re('<path d="M112 112l20 320c.95 18.49 14.4 32 32 32h184c17.67 0 30.87-13.51 32-32l20-320" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32"></path><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="32" d="M80 112h352" fill="currentColor"></path><path d="M192 112V72h0a23.93 23.93 0 0 1 24-24h80a23.93 23.93 0 0 1 24 24h0v40" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32"></path><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32" d="M256 176v224"></path><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32" d="M184 176l8 224"></path><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32" d="M328 176l-8 224"></path>',6),qs=[Fs],Js=V({name:"TrashOutline",render:function(p,g){return d(),_("svg",Es,qs)}}),Hs={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Ws=o("path",{d:"M85.57 446.25h340.86a32 32 0 0 0 28.17-47.17L284.18 82.58c-12.09-22.44-44.27-22.44-56.36 0L57.4 399.08a32 32 0 0 0 28.17 47.17z",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),Gs=o("path",{d:"M250.26 195.39l5.74 122l5.73-121.95a5.74 5.74 0 0 0-5.79-6h0a5.74 5.74 0 0 0-5.68 5.95z",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),Ks=o("path",{d:"M256 397.25a20 20 0 1 1 20-20a20 20 0 0 1-20 20z",fill:"currentColor"},null,-1),Qs=[Ws,Gs,Ks],Xs=V({name:"WarningOutline",render:function(p,g){return d(),_("svg",Hs,Qs)}}),Ys={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Zs=o("path",{d:"M332.41 310.59a115 115 0 0 0-152.8 0",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),eo=o("path",{d:"M393.46 249.54a201.26 201.26 0 0 0-274.92 0",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),to=o("path",{d:"M447.72 182.11a288 288 0 0 0-383.44 0",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),so=o("path",{d:"M256 416a32 32 0 1 1 32-32a32 32 0 0 1-32 32z",fill:"currentColor"},null,-1),oo=[Zs,eo,to,so],lo=V({name:"WifiOutline",render:function(p,g){return d(),_("svg",Ys,oo)}}),U={batchImportAccounts(n){return J.post("/mailbox/batch-import",n)},getAccountsList(n){return J.get("/mailbox/accounts",{params:n})},startVerificationTask(n){return J.post("/mailbox/verify",n)},getBatchOperationStatus(n){return J.get(`/mailbox/batch-operation/${n}`)},controlTask(n){return J.post("/mailbox/task-control",n)},getTaskSchedulerStatus(){return J.get("/mailbox/scheduler-status")},getMailboxStatistics(){return J.get("/mailbox/statistics")},deleteAccount(n){return J.delete(`/mailbox/accounts/${n}`)},batchDeleteAccounts(n){return J.post("/mailbox/batch-delete",{account_ids:n})},toggleAccountStatus(n,p){return J.put(`/mailbox/accounts/${n}/status`,{is_disabled:p})},batchDisableAccounts(n){return J.post("/mailbox/batch-disable",{account_ids:n})},parseBatchImportText(n){const p=n.split(`
`).filter(T=>T.trim()),g=[];for(const T of p){const C=T.trim();if(!C)continue;const b=["----","---","--","	"," "];let u="",c="";for(const v of b)if(C.includes(v)){const M=C.split(v);if(M.length>=2){u=M[0].trim(),c=M[1].trim();break}}u&&c&&/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(u)&&g.push({email:u,password:c})}return g},validateEmail(n){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(n)},formatImportPreview(n){const p=[],g=[];return n.forEach(T=>{this.validateEmail(T.email)&&T.password?p.push(T):g.push(`${T.email} - 格式错误`)}),{valid:p,invalid:g,summary:{total:n.length,valid:p.length,invalid:g.length}}},getStatusOptions(){return[{label:"全部状态",value:""},{label:"登录成功",value:"success"},{label:"登录失败",value:"failed"},{label:"未登录",value:"pending"}]},getVerificationStatusOptions(){return[{label:"全部状态",value:""},{label:"未验证",value:"unverified"},{label:"验证成功",value:"verified"},{label:"验证失败",value:"failed"}]},getImportSourceOptions(){return[{label:"全部来源",value:""},{label:"手动添加",value:"manual"},{label:"批量导入",value:"batch_import"},{label:"API导入",value:"api"}]},getProxyConfig(){return J.get("/mailbox/proxy-config")},setProxyConfig(n){return J.post("/mailbox/proxy-config",n)},deleteProxyConfig(){return J.delete("/mailbox/proxy-config")},testProxyConfig(n){return J.post("/mailbox/proxy-config/test",n)},formatStatus(n){return{success:{text:"成功",type:"success"},failed:{text:"失败",type:"error"},pending:{text:"未登录",type:"warning"},verified:{text:"验证成功",type:"success"},unverified:{text:"未验证",type:"info"},active:{text:"活跃",type:"success"},inactive:{text:"非活跃",type:"warning"},disabled:{text:"已禁用",type:"error"}}[n]||{text:n,type:"info"}},formatLoginStatus(n){const p=this.formatStatus(n.login_status);if(n.last_login_time){const g=new Date(n.last_login_time).toLocaleString();return{...p,text:`${p.text}`,tooltip:`最后登录: ${g}`}}return p},formatVerificationStatus(n){const p=this.formatStatus(n.verification_status);let g="";return n.last_verification_time&&(g=`最后验证: ${new Date(n.last_verification_time).toLocaleString()}`),n.verification_error&&n.verification_status==="failed"&&(g+=g?`
错误信息: ${n.verification_error}`:`错误信息: ${n.verification_error}`),{...p,tooltip:g||void 0}}},no={class:"batch-import-modal"},ao={key:0,class:"step-content"},ro={class:"step-actions"},io={key:1,class:"step-content"},uo={key:0,class:"text-red-500"},co={key:0,class:"mb-4"},po={key:0,class:"text-gray-500 mt-2"},vo={key:1,class:"mb-4"},fo={class:"step-actions"},_o={key:2,class:"step-content"},go={key:0,class:"text-center"},mo={key:0},ko={key:1},yo=V({__name:"BatchImportModal",props:{visible:{type:Boolean}},emits:["update:visible","success"],setup(n,{emit:p}){const g=n,T=p,C=ie(),b=I(1),u=I("process"),c=I(!1),v=I(!1),M=oe({importText:"",source:"批量导入",tags:[],description:"",autoVerify:!0}),f=I({valid:[],invalid:[],summary:{total:0,valid:0,invalid:0}}),z=I(null),E=Z({get:()=>g.visible,set:r=>T("update:visible",r)}),B={importText:{required:!0,message:"请输入导入数据",trigger:"blur"},source:{required:!0,message:"请输入导入来源",trigger:"blur"}},R=[{title:"序号",key:"index",width:60,render:(r,a)=>a+1},{title:"邮箱地址",key:"email",ellipsis:{tooltip:!0}},{title:"密码",key:"password",width:120,render:r=>"●".repeat(r.password.length)}],j=async()=>{if(!M.importText.trim()){C.error("请输入导入数据");return}c.value=!0;try{const r=U.parseBatchImportText(M.importText);if(f.value=U.formatImportPreview(r),f.value.valid.length===0){C.error("没有解析到有效的账户数据，请检查格式");return}b.value=2}catch(r){C.error("解析数据失败"),console.error(r)}finally{c.value=!1}},k=async()=>{v.value=!0;try{const r={accounts:f.value.valid,source:M.source,tags:M.tags,auto_verify:M.autoVerify,description:M.description},a=await U.batchImportAccounts(r);console.log("导入响应:",JSON.stringify(a));const l=a.data||a;z.value={success:l.success,message:l.message,data:l.data},console.log("导入结果:",JSON.stringify(z.value)),b.value=3,u.value=l.success?"finish":"error",l.success&&T("success",l.data)}catch(r){console.log("导入错误:",JSON.stringify(r)),z.value={success:!1,message:r.message||"导入失败"},b.value=3,u.value="error"}finally{v.value=!1}},D=()=>{C.info("功能开发中...")},G=()=>{m(),E.value=!1},m=()=>{b.value=1,u.value="process",M.importText="",M.source="批量导入",M.tags=[],M.description="",M.autoVerify=!0,f.value={valid:[],invalid:[],summary:{total:0,valid:0,invalid:0}},z.value=null};return ae(E,r=>{r||m()}),(r,a)=>(d(),K(e(ge),{show:E.value,"onUpdate:show":a[9]||(a[9]=l=>E.value=l),preset:"dialog",title:"批量导入邮箱",style:{width:"800px"}},{default:s(()=>[o("div",no,[t(e(tt),{current:b.value,status:u.value},{default:s(()=>[t(e(ye),{title:"输入数据"}),t(e(ye),{title:"预览确认"}),t(e(ye),{title:"导入结果"})]),_:1},8,["current","status"]),b.value===1?(d(),_("div",ao,[t(e(we),{type:"info",class:"mb-4"},{header:s(()=>a[10]||(a[10]=[w("导入格式说明")])),default:s(()=>[a[11]||(a[11]=o("div",null,[o("p",null,[w("每行一个邮箱账户，格式："),o("code",null,"邮箱地址----密码")]),o("p",null,[w("支持的分隔符："),o("code",null,"----"),w("、"),o("code",null,"---"),w("、"),o("code",null,"--"),w("、制表符、空格")]),o("p",null,"示例："),o("pre",null,`<EMAIL>----password123
<EMAIL>----password456`)],-1))]),_:1,__:[11]}),t(e($e),{ref:"formRef",model:M,rules:B},{default:s(()=>[t(e(W),{label:"导入数据",path:"importText"},{default:s(()=>[t(e(ce),{value:M.importText,"onUpdate:value":a[0]||(a[0]=l=>M.importText=l),type:"textarea",placeholder:"请输入邮箱账户数据，每行一个...",rows:10,"show-count":""},null,8,["value"])]),_:1}),t(e(W),{label:"导入来源",path:"source"},{default:s(()=>[t(e(ce),{value:M.source,"onUpdate:value":a[1]||(a[1]=l=>M.source=l),placeholder:"例如：客户提供、爬虫获取等"},null,8,["value"])]),_:1}),t(e(W),{label:"标签"},{default:s(()=>[t(e(st),{value:M.tags,"onUpdate:value":a[2]||(a[2]=l=>M.tags=l)},null,8,["value"])]),_:1}),t(e(W),{label:"描述"},{default:s(()=>[t(e(ce),{value:M.description,"onUpdate:value":a[3]||(a[3]=l=>M.description=l),placeholder:"可选，描述本次导入的目的或备注"},null,8,["value"])]),_:1}),t(e(W),null,{default:s(()=>[t(e(xe),{checked:M.autoVerify,"onUpdate:checked":a[4]||(a[4]=l=>M.autoVerify=l)},{default:s(()=>a[12]||(a[12]=[w(" 导入后自动验证邮箱 ")])),_:1,__:[12]},8,["checked"])]),_:1})]),_:1},8,["model"]),o("div",ro,[t(e(H),{justify:"end"},{default:s(()=>[t(e(O),{onClick:a[5]||(a[5]=l=>E.value=!1)},{default:s(()=>a[13]||(a[13]=[w("取消")])),_:1,__:[13]}),t(e(O),{type:"primary",onClick:j,loading:c.value},{default:s(()=>a[14]||(a[14]=[w(" 解析预览 ")])),_:1,__:[14]},8,["loading"])]),_:1})])])):A("",!0),b.value===2?(d(),_("div",io,[t(e(we),{type:f.value.summary.invalid>0?"warning":"success",class:"mb-4"},{header:s(()=>a[15]||(a[15]=[w("解析结果")])),default:s(()=>[o("div",null,[o("p",null,"总计："+P(f.value.summary.total)+" 行",1),o("p",null,"有效："+P(f.value.summary.valid)+" 个账户",1),f.value.summary.invalid>0?(d(),_("p",uo," 无效："+P(f.value.summary.invalid)+" 行（格式错误） ",1)):A("",!0)])]),_:1},8,["type"]),f.value.valid.length>0?(d(),_("div",co,[a[16]||(a[16]=o("h4",null,"有效账户预览（前10个）：",-1)),t(e(de),{columns:R,data:f.value.valid.slice(0,10),pagination:!1,size:"small"},null,8,["data"]),f.value.valid.length>10?(d(),_("p",po," 还有 "+P(f.value.valid.length-10)+" 个账户未显示... ",1)):A("",!0)])):A("",!0),f.value.invalid.length>0?(d(),_("div",vo,[a[17]||(a[17]=o("h4",{class:"text-red-500"},"无效数据：",-1)),t(e(ot),{style:{"max-height":"200px"}},{default:s(()=>[(d(!0),_(Ce,null,Ve(f.value.invalid,(l,$)=>(d(),_("div",{key:$,class:"text-red-500 text-sm"},P(l),1))),128))]),_:1})])):A("",!0),o("div",fo,[t(e(H),{justify:"end"},{default:s(()=>[t(e(O),{onClick:a[6]||(a[6]=l=>b.value=1)},{default:s(()=>a[18]||(a[18]=[w("返回修改")])),_:1,__:[18]}),t(e(O),{type:"primary",onClick:k,disabled:f.value.valid.length===0,loading:v.value},{default:s(()=>[w(" 确认导入 ("+P(f.value.valid.length)+" 个账户) ",1)]),_:1},8,["disabled","loading"])]),_:1})])])):A("",!0),b.value===3?(d(),_("div",_o,[z.value?(d(),_("div",go,[t(e(lt),{status:z.value.success?"success":"error",title:z.value.success?"导入成功":"导入失败",description:z.value.message},{footer:s(()=>[z.value.success&&z.value.data?(d(),_("div",mo,[o("p",null,"操作ID："+P(z.value.data.operation_id),1),o("p",null,"总数量："+P(z.value.data.total_count),1),o("p",null,"状态："+P(z.value.data.status),1),t(e(H),{justify:"center",class:"mt-4"},{default:s(()=>[t(e(O),{onClick:D},{default:s(()=>a[19]||(a[19]=[w("查看进度")])),_:1,__:[19]}),t(e(O),{type:"primary",onClick:G},{default:s(()=>a[20]||(a[20]=[w("完成")])),_:1,__:[20]})]),_:1})])):(d(),_("div",ko,[t(e(H),{justify:"center",class:"mt-4"},{default:s(()=>[t(e(O),{onClick:a[7]||(a[7]=l=>b.value=1)},{default:s(()=>a[21]||(a[21]=[w("重新导入")])),_:1,__:[21]}),t(e(O),{onClick:a[8]||(a[8]=l=>E.value=!1)},{default:s(()=>a[22]||(a[22]=[w("关闭")])),_:1,__:[22]})]),_:1})]))]),_:1},8,["status","title","description"])])):A("",!0)])):A("",!0)])]),_:1},8,["show"]))}}),ho=re(yo,[["__scopeId","data-v-bcaf745d"]]),wo={class:"text-gray-500 text-sm"},xo={class:"space-y-2"},bo={class:"flex items-center"},$o={class:"ml-2 font-medium"},Co={key:0,class:"text-sm text-gray-600 space-y-1"},So={key:0},Mo={key:1},To={key:2},Po={key:3},Oo={key:1,class:"text-sm text-red-600"},Lo=V({__name:"ProxyConfigModal",props:{visible:{type:Boolean}},emits:["update:visible","success"],setup(n,{emit:p}){const g=n,T=p,C=I(!1),b=I(),u=ie(),c=oe({enabled:!1,proxy_url:"",proxy_type:"http"}),v=I(null),M=I(!1),f=I(!1),z=I(!1),E=I(!1),B=[{label:"HTTP/HTTPS",value:"http"},{label:"SOCKS5",value:"socks5"}],R={proxy_url:[{required:!0,trigger:["blur","input"],validator:(m,r)=>c.enabled?r?/^(https?|socks5):\/\/.+/.test(r)?!0:new Error("代理地址格式错误，应为: protocol://[username:password@]host:port"):new Error("请输入代理地址"):!0}]};ae(()=>g.visible,m=>{C.value=m,m&&(j(),v.value=null)}),ae(C,m=>{T("update:visible",m)}),ae(()=>c.enabled,()=>{v.value=null}),ae(()=>c.proxy_url,()=>{v.value=null});const j=async()=>{try{console.log("[ProxyConfigModal] 开始加载代理配置...");const m=await U.getProxyConfig();console.log("[ProxyConfigModal] API响应:",m),console.log("[ProxyConfigModal] response.data:",m.data);let r=m.data;m.data&&typeof m.data.data<"u"?(r=m.data.data,console.log("[ProxyConfigModal] 检测到嵌套结构，使用response.data.data:",r)):console.log("[ProxyConfigModal] 使用直接结构，response.data:",r),r&&typeof r=="object"?(console.log("[ProxyConfigModal] 更新前的formData:",{...c}),c.enabled=!!r.enabled,c.proxy_url=String(r.proxy_url||""),c.proxy_type=String(r.proxy_type||"http"),console.log("[ProxyConfigModal] 更新后的formData:",{...c}),E.value=!!r.enabled||!!r.proxy_url):console.warn("[ProxyConfigModal] 未找到有效的代理配置数据，proxyData:",r)}catch(m){console.error("加载代理配置失败:",m),u.error("加载代理配置失败")}},k=async()=>{console.log("[ProxyConfigModal] 开始测试连接..."),M.value=!0,v.value=null;try{console.log("[ProxyConfigModal] 发送测试请求，配置:",c);const m=await U.testProxyConfig(c);console.log("[ProxyConfigModal] 测试响应原始数据:",m),console.log("[ProxyConfigModal] response.data:",m.data);let r=m.data;m.data&&typeof m.data.data<"u"?(r=m.data.data,console.log("[ProxyConfigModal] 使用嵌套结构 response.data.data:",r)):console.log("[ProxyConfigModal] 使用直接结构 response.data:",r),r?(console.log("[ProxyConfigModal] 设置测试结果:",r),console.log("[ProxyConfigModal] 测试结果详情:",{success:r.success,ip:r.ip,country:r.country,city:r.city,region:r.region,isp:r.isp,latency_ms:r.latency_ms,error:r.error}),v.value=r,r.success?u.success("代理连接测试成功"):u.warning("代理连接测试失败")):console.warn("[ProxyConfigModal] 测试数据为空")}catch(m){console.error("测试代理连接失败:",m),u.error("测试代理连接失败"),v.value={success:!1,error:"网络请求失败"}}finally{M.value=!1}},D=async()=>{if(b.value)try{await b.value.validate(),f.value=!0,await U.setProxyConfig(c),u.success("代理配置保存成功"),T("success"),C.value=!1}catch(m){console.error("保存代理配置失败:",m),m instanceof Error?u.error(`保存失败: ${m.message}`):u.error("保存代理配置失败")}finally{f.value=!1}},G=async()=>{z.value=!0;try{await U.deleteProxyConfig(),Object.assign(c,{enabled:!1,proxy_url:"",proxy_type:"http"}),v.value=null,E.value=!1,u.success("代理配置已重置"),T("success")}catch(m){console.error("重置代理配置失败:",m),u.error("重置代理配置失败")}finally{z.value=!1}};return(m,r)=>(d(),K(e(ge),{show:C.value,"onUpdate:show":r[4]||(r[4]=a=>C.value=a),preset:"dialog",title:"代理配置",style:{width:"600px"}},{action:s(()=>[t(e(H),null,{default:s(()=>[t(e(O),{onClick:r[3]||(r[3]=a=>C.value=!1)},{default:s(()=>r[6]||(r[6]=[w("取消")])),_:1,__:[6]}),c.enabled||E.value?(d(),K(e(O),{key:0,type:"error",ghost:"",onClick:G,loading:z.value},{default:s(()=>r[7]||(r[7]=[w(" 重置 ")])),_:1,__:[7]},8,["loading"])):A("",!0),t(e(O),{type:"primary",onClick:D,loading:f.value},{default:s(()=>r[8]||(r[8]=[w(" 保存 ")])),_:1,__:[8]},8,["loading"])]),_:1})]),default:s(()=>[t(e($e),{ref_key:"formRef",ref:b,model:c,rules:R,"label-placement":"left","label-width":"120px"},{default:s(()=>[t(e(W),{label:"启用代理",path:"enabled"},{default:s(()=>[t(e(nt),{value:c.enabled,"onUpdate:value":r[0]||(r[0]=a=>c.enabled=a)},null,8,["value"]),r[5]||(r[5]=o("span",{class:"ml-2 text-gray-500"},"启用后所有网络请求将通过代理服务器",-1))]),_:1,__:[5]}),c.enabled?(d(),_(Ce,{key:0},[t(e(W),{label:"代理类型",path:"proxy_type"},{default:s(()=>[t(e(le),{value:c.proxy_type,"onUpdate:value":r[1]||(r[1]=a=>c.proxy_type=a),options:B,placeholder:"选择代理类型"},null,8,["value"])]),_:1}),t(e(W),{label:"代理地址",path:"proxy_url"},{default:s(()=>[t(e(ce),{value:c.proxy_url,"onUpdate:value":r[2]||(r[2]=a=>c.proxy_url=a),placeholder:"例如: http://username:<EMAIL>:8080",clearable:""},null,8,["value"])]),_:1})],64)):A("",!0),t(e(W),{label:"连接测试"},{default:s(()=>[t(e(H),null,{default:s(()=>[t(e(O),{type:"primary",loading:M.value,onClick:k,disabled:!1},{default:s(()=>[w(P(c.enabled?"测试代理连接":"测试直连"),1)]),_:1},8,["loading"]),o("span",wo,P(c.enabled?"测试通过代理服务器的连接":"测试当前网络的直接连接"),1)]),_:1})]),_:1}),v.value?(d(),K(e(W),{key:1,label:"测试结果"},{default:s(()=>[t(e(ne),{size:"small",class:Ee(v.value.success?"border-green-200":"border-red-200")},{default:s(()=>[o("div",xo,[o("div",bo,[t(e(F),{color:v.value.success?"#10b981":"#ef4444",size:"16"},{default:s(()=>[v.value.success?(d(),K(e(Me),{key:0})):(d(),K(e(Te),{key:1}))]),_:1},8,["color"]),o("span",$o,P(v.value.success?"连接成功":"连接失败"),1)]),v.value.success?(d(),_("div",Co,[v.value.ip?(d(),_("div",So,"IP地址: "+P(v.value.ip),1)):A("",!0),v.value.country?(d(),_("div",Mo," 位置: "+P(v.value.country)+" "+P(v.value.region)+" "+P(v.value.city),1)):A("",!0),v.value.isp?(d(),_("div",To,"ISP: "+P(v.value.isp),1)):A("",!0),v.value.latency_ms?(d(),_("div",Po,"延迟: "+P(v.value.latency_ms)+"ms",1)):A("",!0)])):A("",!0),!v.value.success&&v.value.error?(d(),_("div",Oo," 错误: "+P(v.value.error),1)):A("",!0)])]),_:1},8,["class"])]),_:1})):A("",!0)]),_:1},8,["model"])]),_:1},8,["show"]))}}),zo=re(Lo,[["__scopeId","data-v-22196bc9"]]),Io={class:"verification-task-modal"},Do={class:"modal-actions"},jo=V({__name:"VerificationTaskModal",props:{visible:{type:Boolean},selectedAccounts:{}},emits:["update:visible","success"],setup(n,{emit:p}){const g=n,T=p,C=ie(),b=I(!1),u=oe({verificationType:"login",concurrentLimit:10,retryLimit:3,timeoutSeconds:30,skipRecentVerified:!0,updateAccountStatus:!0}),c=Z({get:()=>g.visible,set:j=>T("update:visible",j)}),v=Z(()=>g.selectedAccounts.length),M=Z(()=>{const j=v.value,k=u.concurrentLimit,D=u.timeoutSeconds+5;if(j===0)return"0分钟";const G=Math.ceil(j/k)*D,m=Math.ceil(G/60);if(m<60)return`约 ${m} 分钟`;{const r=Math.floor(m/60),a=m%60;return`约 ${r} 小时 ${a} 分钟`}}),f=[{label:"登录验证",value:"login"},{label:"健康检查",value:"health_check"},{label:"会话测试",value:"session_test"}],z={verificationType:{required:!0,message:"请选择验证类型",trigger:"change"},concurrentLimit:{required:!0,type:"number",min:1,max:50,message:"并发限制必须在1-50之间",trigger:"blur"},retryLimit:{required:!0,type:"number",min:0,max:10,message:"重试次数必须在0-10之间",trigger:"blur"}},E=j=>{const k=f.find(D=>D.value===j);return(k==null?void 0:k.label)||j},B=async()=>{if(v.value===0){C.error("请先选择要验证的账户");return}b.value=!0;try{const k={account_emails:g.selectedAccounts.map(m=>m.email),verification_type:u.verificationType,concurrent_limit:u.concurrentLimit,retry_limit:u.retryLimit},D=await U.startVerificationTask(k);console.log("验证任务响应:",JSON.stringify(D));const G=D.data||D;G.success?(C.success("验证任务已创建，正在后台执行"),T("success",G.data),c.value=!1):C.error(G.message||"创建验证任务失败")}catch(j){C.error(j.message||"创建验证任务失败"),console.error(j)}finally{b.value=!1}},R=()=>{u.verificationType="login",u.concurrentLimit=10,u.retryLimit=3,u.timeoutSeconds=30,u.skipRecentVerified=!0,u.updateAccountStatus=!0};return ae(c,j=>{j||R()}),(j,k)=>(d(),K(e(ge),{show:c.value,"onUpdate:show":k[7]||(k[7]=D=>c.value=D),preset:"dialog",title:"启动验证任务",style:{width:"600px"}},{default:s(()=>[o("div",Io,[t(e(we),{type:"info",class:"mb-4"},{header:s(()=>k[8]||(k[8]=[w("验证任务说明")])),default:s(()=>[k[9]||(k[9]=o("div",null,[o("p",null,"将对选中的邮箱账户进行登录验证，检查账户的有效性。"),o("p",null,"验证过程将在后台异步执行，您可以在任务控制面板中查看进度。")],-1))]),_:1,__:[9]}),t(e($e),{ref:"formRef",model:u,rules:z,"label-placement":"left","label-width":"120px"},{default:s(()=>[t(e(W),{label:"选中账户数"},{default:s(()=>[t(e(se),{type:"info"},{default:s(()=>[w(P(v.value)+" 个账户",1)]),_:1})]),_:1}),t(e(W),{label:"验证类型",path:"verificationType"},{default:s(()=>[t(e(le),{value:u.verificationType,"onUpdate:value":k[0]||(k[0]=D=>u.verificationType=D),options:f,placeholder:"请选择验证类型"},null,8,["value"])]),_:1}),t(e(W),{label:"并发限制",path:"concurrentLimit"},{feedback:s(()=>k[10]||(k[10]=[o("span",{class:"text-gray-500"},"建议设置为 5-20，过高可能导致IP被封",-1)])),default:s(()=>[t(e(he),{value:u.concurrentLimit,"onUpdate:value":k[1]||(k[1]=D=>u.concurrentLimit=D),min:1,max:50,placeholder:"同时验证的账户数量"},null,8,["value"])]),_:1}),t(e(W),{label:"重试次数",path:"retryLimit"},{default:s(()=>[t(e(he),{value:u.retryLimit,"onUpdate:value":k[2]||(k[2]=D=>u.retryLimit=D),min:0,max:10,placeholder:"验证失败时的重试次数"},null,8,["value"])]),_:1}),t(e(W),{label:"超时设置"},{default:s(()=>[t(e(he),{value:u.timeoutSeconds,"onUpdate:value":k[3]||(k[3]=D=>u.timeoutSeconds=D),min:10,max:300,placeholder:"单个账户验证超时时间（秒）"},null,8,["value"])]),_:1}),t(e(W),null,{default:s(()=>[t(e(xe),{checked:u.skipRecentVerified,"onUpdate:checked":k[4]||(k[4]=D=>u.skipRecentVerified=D)},{default:s(()=>k[11]||(k[11]=[w(" 跳过最近24小时内已验证的账户 ")])),_:1,__:[11]},8,["checked"])]),_:1}),t(e(W),null,{default:s(()=>[t(e(xe),{checked:u.updateAccountStatus,"onUpdate:checked":k[5]||(k[5]=D=>u.updateAccountStatus=D)},{default:s(()=>k[12]||(k[12]=[w(" 根据验证结果自动更新账户状态 ")])),_:1,__:[12]},8,["checked"])]),_:1})]),_:1},8,["model"]),t(e(ne),{title:"预估信息",size:"small",class:"mb-4"},{default:s(()=>[t(e(at),{column:2,size:"small"},{default:s(()=>[t(e(fe),{label:"预计耗时"},{default:s(()=>[w(P(M.value),1)]),_:1}),t(e(fe),{label:"并发数"},{default:s(()=>[w(P(u.concurrentLimit),1)]),_:1}),t(e(fe),{label:"总账户数"},{default:s(()=>[w(P(v.value),1)]),_:1}),t(e(fe),{label:"验证类型"},{default:s(()=>[w(P(E(u.verificationType)),1)]),_:1})]),_:1})]),_:1}),o("div",Do,[t(e(H),{justify:"end"},{default:s(()=>[t(e(O),{onClick:k[6]||(k[6]=D=>c.value=!1)},{default:s(()=>k[13]||(k[13]=[w("取消")])),_:1,__:[13]}),t(e(O),{type:"primary",onClick:B,loading:b.value,disabled:v.value===0},{default:s(()=>k[14]||(k[14]=[w(" 启动验证任务 ")])),_:1,__:[14]},8,["loading","disabled"])]),_:1})])])]),_:1},8,["show"]))}}),No=re(jo,[["__scopeId","data-v-c84d343d"]]),Bo={class:"task-control-panel"},Uo=V({__name:"TaskControlPanel",props:{visible:{type:Boolean}},emits:["update:visible","refresh"],setup(n,{emit:p}){const g=n,T=p,C=ie(),b=rt(),u=I(!1),c=I(!1),v=I([]),M=I([]),f=Z({get:()=>g.visible,set:l=>T("update:visible",l)}),z=Z(()=>{const l={running:0,stopped:0,paused:0,error:0};return v.value.forEach($=>{switch($.status){case"running":l.running++;break;case"stopped":l.stopped++;break;case"paused":l.paused++;break;case"error":l.error++;break}}),l}),E=oe({page:1,pageSize:10,itemCount:0,showSizePicker:!0,pageSizes:[10,20,50]}),B=Z(()=>[{title:"任务名称",key:"task_name",width:200,ellipsis:{tooltip:!0}},{title:"任务类型",key:"task_type",width:120,render:l=>({batch_verification:"批量验证",health_check:"健康检查",cleanup:"清理任务"})[l.task_type]||l.task_type},{title:"状态",key:"status",width:100,render:l=>{const q={running:{text:"运行中",type:"success"},stopped:{text:"已停止",type:"info"},paused:{text:"已暂停",type:"warning"},error:{text:"错误",type:"error"}}[l.status]||{text:l.status,type:"info"};return L(se,{type:q.type},{default:()=>q.text})}},{title:"运行次数",key:"run_count",width:100},{title:"错误次数",key:"error_count",width:100,render:l=>l.error_count>0?L(se,{type:"error"},{default:()=>l.error_count}):l.error_count},{title:"最后运行",key:"last_run_at",width:150,render:l=>l.last_run_at?new Date(l.last_run_at).toLocaleString():"-"},{title:"操作",key:"actions",width:200,render:l=>L(H,[L(O,{size:"small",type:l.status==="running"?"warning":"primary",onClick:()=>D(l)},{default:()=>l.status==="running"?"暂停":"启动",icon:()=>L(F,null,{default:()=>l.status==="running"?L(_s):L(De)})}),L(O,{size:"small",type:"error",onClick:()=>G(l),disabled:l.status==="stopped"},{default:()=>"停止",icon:()=>L(F,null,{default:()=>L(Ne)})}),L(O,{size:"small",onClick:()=>m(l)},{default:()=>"重置"})])}]),R=Z(()=>[{title:"操作ID",key:"operation_id",width:120,ellipsis:{tooltip:!0}},{title:"操作类型",key:"operation_type",width:100,render:l=>({import:"批量导入",verify:"批量验证",disable:"批量禁用",enable:"批量启用"})[l.operation_type]||l.operation_type},{title:"状态",key:"status",width:100,render:l=>{const q={pending:{text:"等待中",type:"info"},running:{text:"运行中",type:"warning"},completed:{text:"已完成",type:"success"},failed:{text:"失败",type:"error"},cancelled:{text:"已取消",type:"info"}}[l.status]||{text:l.status,type:"info"};return L(se,{type:q.type},{default:()=>q.text})}},{title:"进度",key:"progress",width:150,render:l=>{const $=l.total_count>0?Math.round(l.processed_count/l.total_count*100):0;return L("div",[L(it,{type:"line",percentage:$,showIndicator:!1,height:8}),L("div",{class:"text-xs text-gray-500 mt-1"},`${l.processed_count}/${l.total_count}`)])}},{title:"成功/失败",key:"result",width:100,render:l=>L("div",{class:"text-xs"},[L("div",{class:"text-green-600"},`成功: ${l.success_count}`),L("div",{class:"text-red-600"},`失败: ${l.failed_count}`)])},{title:"创建时间",key:"created_at",width:150,render:l=>new Date(l.created_at).toLocaleString()}]),j=async()=>{u.value=!0;try{const l=await U.getTaskSchedulerStatus();l.success&&l.data&&(v.value=l.data)}catch(l){C.error("加载任务状态失败"),console.error(l)}finally{u.value=!1}},k=async()=>{c.value=!0,c.value=!1},D=async l=>{try{const $=l.status==="running"?"pause":"start";await U.controlTask({action:$,task_id:l.task_name}),C.success(`任务${$==="start"?"启动":"暂停"}成功`),j(),T("refresh")}catch($){C.error($.message||"操作失败")}},G=async l=>{try{await U.controlTask({action:"stop",task_id:l.task_name}),C.success("任务停止成功"),j(),T("refresh")}catch($){C.error($.message||"停止任务失败")}},m=async l=>{b.warning({title:"确认重置",content:`确定要重置任务 "${l.task_name}" 吗？这将清除运行计数和错误记录。`,positiveText:"确认",negativeText:"取消",onPositiveClick:async()=>{try{await U.controlTask({action:"reset",task_id:l.task_name}),C.success("任务重置成功"),j(),T("refresh")}catch($){C.error($.message||"重置任务失败")}}})},r=async()=>{b.info({title:"确认启动",content:"确定要启动所有已停止的任务吗？",positiveText:"确认",negativeText:"取消",onPositiveClick:async()=>{const l=v.value.filter($=>$.status==="stopped");for(const $ of l)try{await U.controlTask({action:"start",task_id:$.task_name})}catch(q){console.error(`启动任务 ${$.task_name} 失败:`,q)}C.success("批量启动完成"),j(),T("refresh")}})},a=async()=>{b.warning({title:"确认停止",content:"确定要停止所有运行中的任务吗？",positiveText:"确认",negativeText:"取消",onPositiveClick:async()=>{const l=v.value.filter($=>$.status==="running");for(const $ of l)try{await U.controlTask({action:"stop",task_id:$.task_name})}catch(q){console.error(`停止任务 ${$.task_name} 失败:`,q)}C.success("批量停止完成"),j(),T("refresh")}})};return Se(()=>{j(),k()}),(l,$)=>(d(),K(e(ut),{show:f.value,"onUpdate:show":$[0]||($[0]=q=>f.value=q),width:800,placement:"right"},{default:s(()=>[t(e(ct),{title:"定时任务控制面板"},{default:s(()=>[o("div",Bo,[t(e(ne),{title:"任务状态概览",class:"mb-4"},{default:s(()=>[t(e(Ae),{cols:4,"x-gap":12},{default:s(()=>[t(e(X),null,{default:s(()=>[t(e(Y),{label:"运行中",value:z.value.running},null,8,["value"])]),_:1}),t(e(X),null,{default:s(()=>[t(e(Y),{label:"已停止",value:z.value.stopped},null,8,["value"])]),_:1}),t(e(X),null,{default:s(()=>[t(e(Y),{label:"已暂停",value:z.value.paused},null,8,["value"])]),_:1}),t(e(X),null,{default:s(()=>[t(e(Y),{label:"错误",value:z.value.error},null,8,["value"])]),_:1})]),_:1})]),_:1}),t(e(ne),{title:"任务列表"},{"header-extra":s(()=>[t(e(H),null,{default:s(()=>[t(e(O),{onClick:j,loading:u.value},{icon:s(()=>[t(e(F),null,{default:s(()=>[t(e(_e))]),_:1})]),default:s(()=>[$[1]||($[1]=w(" 刷新 "))]),_:1,__:[1]},8,["loading"]),t(e(O),{type:"primary",onClick:r},{icon:s(()=>[t(e(F),null,{default:s(()=>[t(e(De))]),_:1})]),default:s(()=>[$[2]||($[2]=w(" 全部启动 "))]),_:1,__:[2]}),t(e(O),{onClick:a},{icon:s(()=>[t(e(F),null,{default:s(()=>[t(e(Ne))]),_:1})]),default:s(()=>[$[3]||($[3]=w(" 全部停止 "))]),_:1,__:[3]})]),_:1})]),default:s(()=>[t(e(de),{columns:B.value,data:v.value,loading:u.value,pagination:!1,"row-key":q=>q.id},null,8,["columns","data","loading","row-key"])]),_:1}),t(e(ne),{title:"批量操作历史",class:"mt-4"},{"header-extra":s(()=>[t(e(O),{onClick:k,loading:c.value},{icon:s(()=>[t(e(F),null,{default:s(()=>[t(e(_e))]),_:1})]),default:s(()=>[$[4]||($[4]=w(" 刷新 "))]),_:1,__:[4]},8,["loading"])]),default:s(()=>[t(e(de),{columns:R.value,data:M.value,loading:c.value,pagination:E,"row-key":q=>q.id},null,8,["columns","data","loading","pagination","row-key"])]),_:1})])]),_:1})]),_:1},8,["show"]))}}),Ao=re(Uo,[["__scopeId","data-v-252d8a66"]]),Ue={getTaskLogs(n){return J.get("/mailbox/task-logs",{params:n})},getTaskLogDetail(n){return J.get(`/mailbox/task-logs/${n}/details`)},createTaskLog(n){return J.post("/mailbox/task-logs",n)},updateTaskLog(n,p){return J.put(`/mailbox/task-logs/${n}`,p)}},Ro={class:"filter-section mb-4"},Vo={key:0,class:"detail-log-container"},Eo={class:"log-header mb-4"},Fo={class:"text-gray-600"},qo={class:"text-gray-500"},Jo={class:"step-content"},Ho={class:"mb-2"},Wo={key:0,class:"text-sm text-gray-500 mb-2"},Go={key:1,class:"text-red-500 mb-2"},Ko={key:2,class:"response-data"},Qo={key:1,class:"no-steps"},Xo={key:0,class:"mt-4"},Yo=V({__name:"TaskLogPanel",setup(n){const p=ie(),g=I(!1),T=I([]),C=I(!1),b=I(!1),u=I(null),c=oe({dateRange:[Date.now()-4320*60*1e3,Date.now()],operationType:null,status:null,email:""}),v=oe({page:1,pageSize:20,itemCount:0,showSizePicker:!0,pageSizes:[10,20,50,100]}),M=[{label:"批量导入",value:"import"},{label:"邮箱验证",value:"verify"}],f=[{label:"成功",value:"success"},{label:"失败",value:"failed"},{label:"进行中",value:"running"},{label:"等待中",value:"pending"}],z=y=>({success:{type:"success",text:"成功",icon:Me},failed:{type:"error",text:"失败",icon:Te},running:{type:"info",text:"进行中",icon:Be},pending:{type:"warning",text:"等待中",icon:Be}})[y]||{type:"default",text:y,icon:be},E=y=>({import:{text:"批量导入",icon:Fe,color:"#2080f0"},verify:{text:"邮箱验证",icon:Ot,color:"#18a058"}})[y]||{text:y,icon:be,color:"#666"},B=Z(()=>[{title:"时间",key:"start_time",width:160,render:y=>new Date(y.start_time).toLocaleString()},{title:"操作类型",key:"operation_type",width:120,render:y=>{const x=E(y.operation_type);return L(H,{align:"center"},{default:()=>[L(F,{color:x.color},{default:()=>L(x.icon)}),L("span",x.text)]})}},{title:"邮箱地址",key:"email",width:200,ellipsis:{tooltip:!0}},{title:"状态",key:"status",width:100,render:y=>{const x=z(y.status);return L(H,{align:"center"},{default:()=>[L(F,{},{default:()=>L(x.icon)}),L(se,{type:x.type},{default:()=>x.text})]})}},{title:"耗时",key:"duration_ms",width:100,render:y=>y.duration_ms?y.duration_ms<1e3?`${y.duration_ms}ms`:`${(y.duration_ms/1e3).toFixed(1)}s`:"-"},{title:"详细日志",key:"detail_log",width:200,render:y=>L(O,{size:"small",type:"primary",text:!0,onClick:()=>r(y)},{default:()=>"查看详细日志"})},{title:"批次ID",key:"batch_id",width:120,render:y=>y.batch_id||"-"}]),R=async()=>{try{g.value=!0;const y={page:v.page,page_size:v.pageSize};c.operationType&&(y.operation_type=c.operationType),c.status&&(y.status=c.status),c.email&&(y.email=c.email),c.dateRange&&c.dateRange.length===2&&(y.start_time=new Date(c.dateRange[0]).toISOString(),y.end_time=new Date(c.dateRange[1]).toISOString());const x=await Ue.getTaskLogs(y),S=x.data||x;console.log(S),(S.code===200||S.success)&&S.data?(T.value=S.data.items||[],v.itemCount=S.data.total||0):(T.value=[],v.itemCount=0,S.message&&p.error("加载任务日志失败："+S.message))}catch(y){p.error("加载任务日志失败"),console.error(y),T.value=[],v.itemCount=0}finally{g.value=!1}},j=()=>{R()},k=()=>{v.page=1,R()},D=()=>{c.dateRange=[Date.now()-4320*60*1e3,Date.now()],c.operationType=null,c.status=null,c.email="",v.page=1,R()},G=y=>{v.page=y,R()},m=y=>{v.pageSize=y,v.page=1,R()},r=async y=>{C.value=!0,b.value=!0;try{const x=await Ue.getTaskLogDetail(y.id),S=x.data||x;if((S.code===200||S.success)&&S.data){const ee=S.data;ee.task_log&&ee.detail?u.value={taskLog:ee.task_log,detail:ee.detail}:(p.error("详细日志数据格式不正确"),u.value=null)}else p.error("获取详细日志失败："+(S.message||"未知错误")),u.value=null}catch(x){p.error("获取详细日志失败"),console.error(x),u.value=null}finally{b.value=!1}},a=y=>new Date(y).toLocaleString(),l=y=>({success:"success",failed:"error",running:"info",pending:"warning"})[y]||"default",$=y=>({success:"成功",failed:"失败",running:"进行中",pending:"等待中"})[y]||y,q=y=>({success:"success",failed:"error",running:"info"})[y]||"default";return Se(()=>{R()}),(y,x)=>(d(),K(e(ne),{title:"任务日志",class:"task-log-panel"},{"header-extra":s(()=>[t(e(H),null,{default:s(()=>[t(e(O),{onClick:j,size:"small"},{icon:s(()=>[t(e(F),null,{default:s(()=>[t(e(_e))]),_:1})]),default:s(()=>[x[6]||(x[6]=w(" 刷新 "))]),_:1,__:[6]})]),_:1})]),default:s(()=>[o("div",Ro,[t(e(H),null,{default:s(()=>[t(e(dt),{value:c.dateRange,"onUpdate:value":x[0]||(x[0]=S=>c.dateRange=S),type:"daterange",placeholder:"选择时间范围","default-value":[Date.now()-4320*60*1e3,Date.now()],style:{width:"240px"}},null,8,["value","default-value"]),t(e(le),{value:c.operationType,"onUpdate:value":x[1]||(x[1]=S=>c.operationType=S),options:M,placeholder:"操作类型",clearable:"",style:{width:"120px"}},null,8,["value"]),t(e(le),{value:c.status,"onUpdate:value":x[2]||(x[2]=S=>c.status=S),options:f,placeholder:"状态",clearable:"",style:{width:"120px"}},null,8,["value"]),t(e(ce),{value:c.email,"onUpdate:value":x[3]||(x[3]=S=>c.email=S),placeholder:"邮箱地址",clearable:"",style:{width:"200px"}},null,8,["value"]),t(e(O),{onClick:k,type:"primary",size:"small"},{default:s(()=>x[7]||(x[7]=[w("筛选")])),_:1,__:[7]}),t(e(O),{onClick:D,size:"small"},{default:s(()=>x[8]||(x[8]=[w("重置")])),_:1,__:[8]})]),_:1})]),t(e(de),{columns:B.value,data:T.value,loading:g.value,pagination:v,"row-key":S=>S.id,"onUpdate:page":G,"onUpdate:pageSize":m},null,8,["columns","data","loading","pagination","row-key"]),t(e(ge),{show:C.value,"onUpdate:show":x[5]||(x[5]=S=>C.value=S),preset:"card",title:"详细日志",style:{width:"80%","max-width":"1200px"},"mask-closable":!1},{"header-extra":s(()=>[t(e(O),{onClick:x[4]||(x[4]=S=>C.value=!1),quaternary:"",circle:""},{icon:s(()=>[t(e(F),null,{default:s(()=>[t(e(Rt))]),_:1})]),_:1})]),default:s(()=>[t(e(pt),{show:b.value},{default:s(()=>[u.value&&u.value.taskLog?(d(),_("div",Vo,[o("div",Eo,[t(e(H),null,{default:s(()=>[t(e(se),{type:l(u.value.taskLog.status)},{default:s(()=>[w(P($(u.value.taskLog.status)),1)]),_:1},8,["type"]),o("span",Fo,P(u.value.taskLog.email),1),o("span",qo,P(a(u.value.taskLog.start_time)),1)]),_:1})]),u.value.detail&&u.value.detail.steps&&u.value.detail.steps.length>0?(d(),K(e(vt),{key:0},{default:s(()=>[(d(!0),_(Ce,null,Ve(u.value.detail.steps,(S,ee)=>(d(),K(e(ft),{key:ee,type:q(S.status),title:S.step,time:a(S.timestamp)},{default:s(()=>[o("div",Jo,[o("p",Ho,P(S.details),1),S.duration?(d(),_("div",Wo," 耗时: "+P(S.duration)+"ms ",1)):A("",!0),S.error_message?(d(),_("div",Go," 错误: "+P(S.error_message),1)):A("",!0),S.response_data&&Object.keys(S.response_data).length>0?(d(),_("div",Ko,[t(e(ze),{code:JSON.stringify(S.response_data,null,2),language:"json","show-line-numbers":""},null,8,["code"])])):A("",!0)])]),_:2},1032,["type","title","time"]))),128))]),_:1})):(d(),_("div",Qo,[x[10]||(x[10]=o("p",{class:"text-gray-500"},"暂无详细执行步骤记录",-1)),u.value.taskLog&&u.value.taskLog.error_message?(d(),_("div",Xo,[x[9]||(x[9]=o("h4",{class:"mb-2"},"错误信息:",-1)),t(e(ze),{code:u.value.taskLog.error_message,language:"text"},null,8,["code"])])):A("",!0)]))])):A("",!0)]),_:1},8,["show"])]),_:1},8,["show"])]),_:1}))}}),Zo=re(Yo,[["__scopeId","data-v-632c1e68"]]),el={class:"mailbox-management"},tl={class:"proxy-status-tooltip"},sl={class:"status-line"},ol={key:0,class:"status-line"},ll={key:1,class:"status-line"},nl={key:2,class:"status-line"},al={key:3,class:"status-line"},rl={key:4,class:"status-line"},il={class:"error-text"},ul={class:"filter-section mb-4"},cl={class:"statistics-section mb-4"},dl={class:"batch-operations mb-4"},pl=V({__name:"MailboxManagement",setup(n){const p=ie(),g=I(!1),T=I([]),C=I([]),b=I([]),u=I(!1),c=I(!1),v=I(!1),M=I(!1),f=I({status:"loading",statusText:"检测中...",latency:null,proxyInfo:null,location:null,ip:null,error:null});let z=null;const E=I({id:0,stat_date:"",total_accounts:0,active_accounts:0,verified_accounts:0,failed_accounts:0,disabled_accounts:0,new_imports_today:0,verification_success_rate:0,avg_verification_time_ms:0,created_at:""}),B=oe({status:[],verification_status:[],import_source:[],tags:[]}),R=oe({page:1,pageSize:20,itemCount:0,showSizePicker:!0,pageSizes:[10,20,50,100]}),j=U.getStatusOptions(),k=U.getVerificationStatusOptions(),D=U.getImportSourceOptions(),G=Z(()=>[{type:"selection",multiple:!0},{title:"邮箱地址",key:"email",width:200,ellipsis:{tooltip:!0}},{title:"登录状态",key:"login_status",width:120,render:h=>{const i=U.formatLoginStatus(h);return L(Ie,{trigger:"hover",disabled:!i.tooltip},{trigger:()=>L(se,{type:i.type},{default:()=>i.text}),default:()=>i.tooltip})}},{title:"验证状态",key:"verification_status",width:120,render:h=>{const i=U.formatVerificationStatus(h);return L(Ie,{trigger:"hover",disabled:!i.tooltip},{trigger:()=>L(se,{type:i.type},{default:()=>i.text}),default:()=>i.tooltip})}},{title:"导入来源",key:"import_source",width:100},{title:"创建时间",key:"created_at",width:150,render:h=>new Date(h.created_at).toLocaleString()},{title:"最后验证",key:"last_verification_time",width:150,render:h=>h.last_verification_time?new Date(h.last_verification_time).toLocaleString():"-"},{title:"操作",key:"actions",width:150,render:h=>L(H,{},{default:()=>[L(O,{size:"small",type:h.is_disabled?"success":"warning",onClick:()=>Ke(h)},{default:()=>h.is_disabled?"启用":"禁用"}),L(O,{size:"small",type:"error",onClick:()=>Ge(h)},{default:()=>"删除"})]})}]),m=async()=>{g.value=!0;try{const h={page:R.page,page_size:R.pageSize,status:B.status.length>0?B.status:void 0,verification_status:B.verification_status.length>0?B.verification_status:void 0,import_source:B.import_source.length>0?B.import_source:void 0,tags:B.tags.length>0?B.tags:void 0},i=await U.getAccountsList(h);console.log("加载账户列表响应:",JSON.stringify(i));const N=i.data||i;N.success&&N.data?(T.value=N.data.Items||N.data.items||[],R.itemCount=N.data.Total||N.data.total||0):(T.value=[],R.itemCount=0)}catch(h){p.error("加载账户列表失败"),console.error(h),T.value=[],R.itemCount=0}finally{g.value=!1}},r=async()=>{try{const h=await U.getMailboxStatistics(),i=h.data||h;i.success&&i.data&&(E.value=i.data)}catch(h){console.error("加载统计信息失败:",h)}},a=()=>{m(),r()},l=()=>{R.page=1,m()},$=()=>{B.status=[],B.verification_status=[],B.import_source=[],B.tags=[],R.page=1,m()},q=h=>{R.page=h,m()},y=h=>{R.pageSize=h,R.page=1,m()},x=()=>{p.success("批量导入任务已创建"),a()},S=()=>{p.success("代理配置已保存"),me()},ee=()=>{p.success("验证任务已创建"),a()},qe=h=>{b.value=h,C.value=(T.value||[]).filter(i=>h.includes(i.id))},Je=()=>{if(C.value.length===0){p.warning("请先选择要验证的账户");return}v.value=!0},He=async()=>{if(b.value.length===0){p.warning("请先选择要禁用的账户");return}try{await U.batchDisableAccounts(b.value),p.success(`成功禁用 ${b.value.length} 个账户`),b.value=[],C.value=[],a()}catch(h){p.error("批量禁用失败"),console.error(h)}},We=async()=>{if(b.value.length===0){p.warning("请先选择要删除的账户");return}try{await U.batchDeleteAccounts(b.value),p.success(`成功删除 ${b.value.length} 个账户`),b.value=[],C.value=[],a()}catch(h){p.error("批量删除失败"),console.error(h)}},Ge=async h=>{try{await U.deleteAccount(h.id),p.success(`成功删除账户 ${h.email}`),a()}catch(i){p.error(`删除账户 ${h.email} 失败`),console.error(i)}},Ke=async h=>{try{const i=!h.is_disabled;await U.toggleAccountStatus(h.id,i),p.success(`成功${i?"禁用":"启用"}账户 ${h.email}`),a()}catch(i){p.error(`${h.is_disabled?"启用":"禁用"}账户失败`),console.error(i)}},Qe=()=>{r()},me=async()=>{var h,i;try{f.value={status:"loading",statusText:"检测中...",latency:null,proxyInfo:null,location:null,ip:null,error:null};const N=await U.getProxyConfig(),te=((h=N.data)==null?void 0:h.data)||N.data;if(!te){f.value={status:"disabled",statusText:"未启用",latency:null,proxyInfo:null,location:null,ip:null,error:null};return}const Pe=await U.testProxyConfig(te),Q=((i=Pe.data)==null?void 0:i.data)||Pe.data;if(Q){const pe=Q.success,Oe=Q.latency_ms||-1;let ke=null;if(te.enabled&&te.proxy_url)try{const Le=new URL(te.proxy_url),ue=Le.hostname,Ze=Le.port,et=ue.length>6?`${ue.substring(0,3)}***${ue.substring(ue.length-3)}`:`${ue.substring(0,1)}***`;ke=`${te.proxy_type.toUpperCase()} ${et}:${Ze}`}catch{ke=`${te.proxy_type.toUpperCase()} 代理`}let ve=null;Q.country&&(ve=`${Q.country}`,Q.region&&(ve+=` ${Q.region}`),Q.city&&(ve+=` ${Q.city}`)),f.value={status:pe&&Oe<5e3?"success":"error",statusText:te.enabled?pe?"已启用":"连接异常":"未启用",latency:pe?Oe:-1,proxyInfo:ke,location:ve,ip:Q.ip,error:pe?null:Q.error}}else f.value={status:"error",statusText:"检测失败",latency:null,proxyInfo:null,location:null,ip:null,error:"无法获取测试结果"}}catch(N){console.error("代理状态检测失败:",N),f.value={status:"disabled",statusText:"检测失败",latency:null,proxyInfo:null,location:null,ip:null,error:"网络请求失败"}}},Xe=()=>{z&&clearInterval(z),z=setInterval(()=>{f.value.status!=="disabled"&&me()},36e4)},Ye=()=>{z&&(clearInterval(z),z=null)};return Se(()=>{a(),me(),Xe()}),gt(()=>{Ye()}),(h,i)=>(d(),_("div",el,[t(e(ne),{title:"邮箱管理",class:"mb-4"},{"header-extra":s(()=>[t(e(H),null,{default:s(()=>[t(e(O),{type:"primary",onClick:i[0]||(i[0]=N=>u.value=!0)},{icon:s(()=>[t(e(F),null,{default:s(()=>[t(e(Fe))]),_:1})]),default:s(()=>[i[9]||(i[9]=w(" 批量导入 "))]),_:1,__:[9]}),t(e(_t),{trigger:"hover",placement:"bottom","show-arrow":!0,class:"proxy-status-popover-wrapper"},{trigger:s(()=>[t(e(O),{onClick:i[1]||(i[1]=N=>c.value=!0),class:"proxy-config-button"},{icon:s(()=>[t(e(F),null,{default:s(()=>[t(e(je))]),_:1})]),default:s(()=>[i[10]||(i[10]=w(" 代理配置 ")),o("div",{class:Ee(["proxy-status-indicator",{"status-success":f.value.status==="success","status-error":f.value.status==="error","status-loading":f.value.status==="loading","status-disabled":f.value.status==="disabled"}])},[t(e(F),{size:"12"},{default:s(()=>[f.value.status==="success"?(d(),K(e(lo),{key:0})):f.value.status==="error"?(d(),K(e(Te),{key:1})):f.value.status==="loading"?(d(),K(e(Bs),{key:2})):(d(),K(e(Kt),{key:3}))]),_:1})],2)]),_:1,__:[10]})]),default:s(()=>[o("div",tl,[o("div",sl,[t(e(F),{size:"14",class:"status-icon status-icon-server"},{default:s(()=>[t(e(je))]),_:1}),i[11]||(i[11]=o("strong",null,"代理状态：",-1)),o("span",null,P(f.value.statusText),1)]),f.value.latency!==null?(d(),_("div",ol,[t(e(F),{size:"14",class:"status-icon status-icon-speed"},{default:s(()=>[t(e(mt))]),_:1}),i[12]||(i[12]=o("strong",null,"连接延迟：",-1)),o("span",null,P(f.value.latency===-1?"连接失败":`${f.value.latency}ms`),1)])):A("",!0),f.value.proxyInfo?(d(),_("div",ll,[t(e(F),{size:"14",class:"status-icon status-icon-info"},{default:s(()=>[t(e(be))]),_:1}),i[13]||(i[13]=o("strong",null,"代理信息：",-1)),o("span",null,P(f.value.proxyInfo),1)])):A("",!0),f.value.ip?(d(),_("div",nl,[t(e(F),{size:"14",class:"status-icon status-icon-ip"},{default:s(()=>[t(e(Zt))]),_:1}),i[14]||(i[14]=o("strong",null,"IP地址：",-1)),o("span",null,P(f.value.ip),1)])):A("",!0),f.value.location?(d(),_("div",al,[t(e(F),{size:"14",class:"status-icon status-icon-location"},{default:s(()=>[t(e(cs))]),_:1}),i[15]||(i[15]=o("strong",null,"IP位置：",-1)),o("span",null,P(f.value.location),1)])):A("",!0),f.value.error?(d(),_("div",rl,[t(e(F),{size:"14",class:"status-icon status-icon-error"},{default:s(()=>[t(e(Xs))]),_:1}),i[16]||(i[16]=o("strong",null,"错误信息：",-1)),o("span",il,P(f.value.error),1)])):A("",!0)])]),_:1}),t(e(O),{onClick:a},{icon:s(()=>[t(e(F),null,{default:s(()=>[t(e(_e))]),_:1})]),default:s(()=>[i[17]||(i[17]=w(" 刷新 "))]),_:1,__:[17]})]),_:1})]),default:s(()=>[o("div",ul,[t(e(H),null,{default:s(()=>[t(e(le),{value:B.status,"onUpdate:value":i[2]||(i[2]=N=>B.status=N),options:e(j),placeholder:"登录状态",clearable:"",multiple:"",style:{width:"150px"}},null,8,["value","options"]),t(e(le),{value:B.verification_status,"onUpdate:value":i[3]||(i[3]=N=>B.verification_status=N),options:e(k),placeholder:"验证状态",clearable:"",multiple:"",style:{width:"150px"}},null,8,["value","options"]),t(e(le),{value:B.import_source,"onUpdate:value":i[4]||(i[4]=N=>B.import_source=N),options:e(D),placeholder:"导入来源",clearable:"",multiple:"",style:{width:"150px"}},null,8,["value","options"]),t(e(O),{onClick:l,type:"primary"},{default:s(()=>i[18]||(i[18]=[w("筛选")])),_:1,__:[18]}),t(e(O),{onClick:$},{default:s(()=>i[19]||(i[19]=[w("重置")])),_:1,__:[19]})]),_:1})]),o("div",cl,[t(e(Ae),{cols:6,"x-gap":12},{default:s(()=>[t(e(X),null,{default:s(()=>[t(e(Y),{label:"总账户数",value:E.value.total_accounts},null,8,["value"])]),_:1}),t(e(X),null,{default:s(()=>[t(e(Y),{label:"活跃账户",value:E.value.active_accounts},null,8,["value"])]),_:1}),t(e(X),null,{default:s(()=>[t(e(Y),{label:"已验证",value:E.value.verified_accounts},null,8,["value"])]),_:1}),t(e(X),null,{default:s(()=>[t(e(Y),{label:"验证失败",value:E.value.failed_accounts},null,8,["value"])]),_:1}),t(e(X),null,{default:s(()=>[t(e(Y),{label:"已禁用",value:E.value.disabled_accounts},null,8,["value"])]),_:1}),t(e(X),null,{default:s(()=>[t(e(Y),{label:"验证成功率",value:`${E.value.verification_success_rate.toFixed(1)}%`},null,8,["value"])]),_:1})]),_:1})]),o("div",dl,[t(e(H),null,{default:s(()=>[t(e(O),{type:"primary",disabled:b.value.length===0,onClick:Je},{icon:s(()=>[t(e(F),null,{default:s(()=>[t(e(Me))]),_:1})]),default:s(()=>[w(" 批量验证 ("+P(b.value.length)+") ",1)]),_:1},8,["disabled"]),t(e(O),{type:"warning",disabled:b.value.length===0,onClick:He},{icon:s(()=>[t(e(F),null,{default:s(()=>[t(e(xt))]),_:1})]),default:s(()=>[i[20]||(i[20]=w(" 批量禁用 "))]),_:1,__:[20]},8,["disabled"]),t(e(O),{type:"error",disabled:b.value.length===0,onClick:We},{icon:s(()=>[t(e(F),null,{default:s(()=>[t(e(Js))]),_:1})]),default:s(()=>[i[21]||(i[21]=w(" 批量删除 "))]),_:1,__:[21]},8,["disabled"])]),_:1})]),t(e(de),{columns:G.value,data:T.value,loading:g.value,pagination:R,"row-key":N=>N.id,"checked-row-keys":b.value,"onUpdate:checkedRowKeys":qe,"onUpdate:page":q,"onUpdate:pageSize":y},null,8,["columns","data","loading","pagination","row-key","checked-row-keys"])]),_:1}),t(Ao,{visible:M.value,"onUpdate:visible":i[5]||(i[5]=N=>M.value=N),onRefresh:Qe},null,8,["visible"]),t(ho,{visible:u.value,"onUpdate:visible":i[6]||(i[6]=N=>u.value=N),onSuccess:x},null,8,["visible"]),t(zo,{visible:c.value,"onUpdate:visible":i[7]||(i[7]=N=>c.value=N),onSuccess:S},null,8,["visible"]),t(No,{visible:v.value,"onUpdate:visible":i[8]||(i[8]=N=>v.value=N),"selected-accounts":C.value,onSuccess:ee},null,8,["visible","selected-accounts"]),t(Zo,{class:"mt-4"})]))}}),ml=re(pl,[["__scopeId","data-v-52bc975c"]]);export{ml as default};
